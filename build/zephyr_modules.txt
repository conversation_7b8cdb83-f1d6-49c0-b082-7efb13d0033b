"nrf":"/opt/nordic/ncs/v3.0.2/nrf":"/opt/nordic/ncs/v3.0.2/nrf"
"hostap":"/opt/nordic/ncs/v3.0.2/modules/lib/hostap":"${ZEPHYR_HOSTAP_CMAKE_DIR}"
"mcuboot":"/opt/nordic/ncs/v3.0.2/bootloader/mcuboot":"${ZEPHYR_MCUBOOT_CMAKE_DIR}"
"mbedtls":"/opt/nordic/ncs/v3.0.2/modules/crypto/mbedtls":"${ZEPHYR_MBEDTLS_CMAKE_DIR}"
"oberon-psa-crypto":"/opt/nordic/ncs/v3.0.2/modules/crypto/oberon-psa-crypto":"${ZEPHYR_OBERON_PSA_CRYPTO_CMAKE_DIR}"
"trusted-firmware-m":"/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/trusted-firmware-m":"${ZEPHYR_TRUSTED_FIRMWARE_M_CMAKE_DIR}"
"psa-arch-tests":"/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/psa-arch-tests":""
"cjson":"/opt/nordic/ncs/v3.0.2/modules/lib/cjson":"${ZEPHYR_CJSON_CMAKE_DIR}"
"azure-sdk-for-c":"/opt/nordic/ncs/v3.0.2/modules/lib/azure-sdk-for-c":"${ZEPHYR_AZURE_SDK_FOR_C_CMAKE_DIR}"
"cirrus-logic":"/opt/nordic/ncs/v3.0.2/modules/hal/cirrus-logic":"/opt/nordic/ncs/v3.0.2/modules/hal/cirrus-logic"
"openthread":"/opt/nordic/ncs/v3.0.2/modules/lib/openthread":"${ZEPHYR_OPENTHREAD_CMAKE_DIR}"
"suit-generator":"/opt/nordic/ncs/v3.0.2/modules/lib/suit-generator":""
"suit-processor":"/opt/nordic/ncs/v3.0.2/modules/lib/suit-processor":"/opt/nordic/ncs/v3.0.2/modules/lib/suit-processor/ncs"
"memfault-firmware-sdk":"/opt/nordic/ncs/v3.0.2/modules/lib/memfault-firmware-sdk":"/opt/nordic/ncs/v3.0.2/modules/lib/memfault-firmware-sdk/ports/zephyr"
"coremark":"/opt/nordic/ncs/v3.0.2/modules/benchmark/coremark":"${ZEPHYR_COREMARK_CMAKE_DIR}"
"canopennode":"/opt/nordic/ncs/v3.0.2/modules/lib/canopennode":"${ZEPHYR_CANOPENNODE_CMAKE_DIR}"
"chre":"/opt/nordic/ncs/v3.0.2/modules/lib/chre":"/opt/nordic/ncs/v3.0.2/modules/lib/chre/platform/zephyr"
"lz4":"/opt/nordic/ncs/v3.0.2/modules/lib/lz4":"${ZEPHYR_LZ4_CMAKE_DIR}"
"nanopb":"/opt/nordic/ncs/v3.0.2/modules/lib/nanopb":"${ZEPHYR_NANOPB_CMAKE_DIR}"
"tf-m-tests":"/opt/nordic/ncs/v3.0.2/modules/tee/tf-m/tf-m-tests":""
"zscilib":"/opt/nordic/ncs/v3.0.2/modules/lib/zscilib":"/opt/nordic/ncs/v3.0.2/modules/lib/zscilib"
"cmsis":"/opt/nordic/ncs/v3.0.2/modules/hal/cmsis":"${ZEPHYR_CMSIS_CMAKE_DIR}"
"cmsis-dsp":"/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-dsp":"${ZEPHYR_CMSIS_DSP_CMAKE_DIR}"
"cmsis-nn":"/opt/nordic/ncs/v3.0.2/modules/lib/cmsis-nn":"${ZEPHYR_CMSIS_NN_CMAKE_DIR}"
"fatfs":"/opt/nordic/ncs/v3.0.2/modules/fs/fatfs":"${ZEPHYR_FATFS_CMAKE_DIR}"
"hal_nordic":"/opt/nordic/ncs/v3.0.2/modules/hal/nordic":"${ZEPHYR_HAL_NORDIC_CMAKE_DIR}"
"hal_nxp":"/opt/nordic/ncs/v3.0.2/modules/hal/nxp":"${ZEPHYR_HAL_NXP_CMAKE_DIR}"
"hal_st":"/opt/nordic/ncs/v3.0.2/modules/hal/st":"/opt/nordic/ncs/v3.0.2/modules/hal/st"
"hal_stm32":"/opt/nordic/ncs/v3.0.2/modules/hal/stm32":"/opt/nordic/ncs/v3.0.2/modules/hal/stm32"
"hal_tdk":"/opt/nordic/ncs/v3.0.2/modules/hal/tdk":"/opt/nordic/ncs/v3.0.2/modules/hal/tdk"
"hal_wurthelektronik":"/opt/nordic/ncs/v3.0.2/modules/hal/wurthelektronik":"/opt/nordic/ncs/v3.0.2/modules/hal/wurthelektronik"
"liblc3":"/opt/nordic/ncs/v3.0.2/modules/lib/liblc3":"${ZEPHYR_LIBLC3_CMAKE_DIR}"
"libmetal":"/opt/nordic/ncs/v3.0.2/modules/hal/libmetal":"/opt/nordic/ncs/v3.0.2/modules/hal/libmetal"
"littlefs":"/opt/nordic/ncs/v3.0.2/modules/fs/littlefs":"${ZEPHYR_LITTLEFS_CMAKE_DIR}"
"loramac-node":"/opt/nordic/ncs/v3.0.2/modules/lib/loramac-node":"${ZEPHYR_LORAMAC_NODE_CMAKE_DIR}"
"lvgl":"/opt/nordic/ncs/v3.0.2/modules/lib/gui/lvgl":"${ZEPHYR_LVGL_CMAKE_DIR}"
"mipi-sys-t":"/opt/nordic/ncs/v3.0.2/modules/debug/mipi-sys-t":"/opt/nordic/ncs/v3.0.2/modules/debug/mipi-sys-t"
"nrf_wifi":"/opt/nordic/ncs/v3.0.2/modules/lib/nrf_wifi":"${ZEPHYR_NRF_WIFI_CMAKE_DIR}"
"open-amp":"/opt/nordic/ncs/v3.0.2/modules/lib/open-amp":"/opt/nordic/ncs/v3.0.2/modules/lib/open-amp"
"percepio":"/opt/nordic/ncs/v3.0.2/modules/debug/percepio":"${ZEPHYR_PERCEPIO_CMAKE_DIR}"
"picolibc":"/opt/nordic/ncs/v3.0.2/modules/lib/picolibc":"/opt/nordic/ncs/v3.0.2/modules/lib/picolibc"
"segger":"/opt/nordic/ncs/v3.0.2/modules/debug/segger":"${ZEPHYR_SEGGER_CMAKE_DIR}"
"tinycrypt":"/opt/nordic/ncs/v3.0.2/modules/crypto/tinycrypt":"/opt/nordic/ncs/v3.0.2/modules/crypto/tinycrypt"
"uoscore-uedhoc":"/opt/nordic/ncs/v3.0.2/modules/lib/uoscore-uedhoc":"${ZEPHYR_UOSCORE_UEDHOC_CMAKE_DIR}"
"zcbor":"/opt/nordic/ncs/v3.0.2/modules/lib/zcbor":"${ZEPHYR_ZCBOR_CMAKE_DIR}"
"nrfxlib":"/opt/nordic/ncs/v3.0.2/nrfxlib":"${ZEPHYR_NRFXLIB_CMAKE_DIR}"
"nrf_hw_models":"/opt/nordic/ncs/v3.0.2/modules/bsim_hw_models/nrf_hw_models":"/opt/nordic/ncs/v3.0.2/modules/bsim_hw_models/nrf_hw_models"
"connectedhomeip":"/opt/nordic/ncs/v3.0.2/modules/lib/matter":"/opt/nordic/ncs/v3.0.2/modules/lib/matter/config/nrfconnect/chip-module"
