---
type: "manual"
---

# Zephyr Debugging Process Documentation

This document provides a comprehensive guide for debugging Zephyr applications through an iterative build-flash-test-analyze workflow. This process can be applied to any Zephyr project across different hardware platforms and configurations.

## Overview

The debugging process follows an iterative cycle:
1. **Build** → Analyze build errors if any
2. **Flash** → Analyze flashing issues if any
3. **Capture Logs** → Analyze runtime behavior and errors
4. **Troubleshoot** → Apply fixes based on error analysis
5. **Repeat** → Continue until successful completion

## 1. General Zephyr Development Process

### Project Structure
- Ensure your project has the required Zephyr structure:
  - `prj.conf` - Main project configuration
  - `CMakeLists.txt` - Build configuration
  - `src/` directory with source files
  - Optional overlay files for specific configurations

### Environment Setup

#### Nordic Connect SDK (NCS) Environment
```bash
# For NCS v3.0.2 (current workspace)
export NCS_ROOT=/opt/nordic/ncs/v3.0.2
export ZEPHYR_BASE=$NCS_ROOT/zephyr

# Set up NCS toolchain (actual path for NCS v3.0.2)
export PATH=/opt/nordic/ncs/toolchains/ef4fc6722e/bin:$PATH

# Source NCS environment
source $NCS_ROOT/zephyr/zephyr-env.sh

# Verify toolchain
which arm-none-eabi-gcc
# Should point to: /opt/nordic/ncs/toolchains/ef4fc6722e/bin/arm-none-eabi-gcc

# Verify west is available
which west
# Should point to: /opt/nordic/ncs/toolchains/ef4fc6722e/bin/west
```

#### NCS-Specific Tools
```bash
# Verify nrfutil is available
nrfutil --version

# Verify west is configured for NCS
west --version
west list  # Should show NCS modules

# Check NCS workspace status
cd /opt/nordic/ncs/v3.0.2
west status
```

## 2. Build Process

### Basic Build Commands

#### Standard NCS Build
```bash
# Basic build for nRF9151DK (CPU App core)
west build -p -b nrf9151dk/nrf9151/cpuapp

# Build for Thingy:91 X (CPU App core)
west build -p -b thingy91x/nrf9151/cpuapp

# Build with pristine (clean) build
west build -p -b <board_name>

# Build without pristine (incremental)
west build -b <board_name>
```

#### NCS-Specific Build Options
```bash
# Build with board-specific configuration overlay
west build -p -b nrf9151dk/nrf9151/cpuapp -- -DEXTRA_CONF_FILE=boards/nrf9151dk_nrf9151_cpuapp.conf

# Build with multiple overlays
west build -p -b <board_name> -- -DEXTRA_CONF_FILE="overlay1.conf;overlay2.conf"

# Build with shield support (e.g., nRF7002 WiFi)
west build -p -b <board_name> -- -DSHIELD=nrf7002ek

# Build for specific application directory
west build -p -b <board_name> <application_path>
```

#### Common Build Options
```bash
# Specify custom build directory
west build -p -b <board_name> -d <build_directory>

# Build with specific configuration
west build -p -b <board_name> -- -DCONFIG_<OPTION>=y

# Build with debug symbols
west build -p -b <board_name> -- -DCONFIG_DEBUG=y
```

### Build Error Analysis

#### Common Build Errors and Solutions

**Missing Dependencies:**
- Error: `No module named 'west'`
- Solution: Install west: `pip install west`

**Configuration Errors:**
- Error: `CONFIG_* is not defined`
- Solution: Check `prj.conf` and overlay files for missing configurations

**Compilation Errors:**
- Error: `undefined reference to 'function_name'`
- Solution: Check library linking and include paths in `CMakeLists.txt`

**Board/Shield Not Found:**
- Error: `Board <board_name> not found`
- Solution: Verify board name and ensure board files are available

## 3. Flashing Process

### Basic Flashing Commands

#### Standard Flash
```bash
# Flash the built firmware
west flash

# Flash with full erase
west flash --erase

# Flash to specific device (if multiple connected)
west flash --dev-id <device_id>
```

#### Advanced Flashing Options
```bash
# Flash with specific runner
west flash --runner <runner_name>

# Flash with custom options
west flash -- --<runner_specific_option>

# Flash specific hex file
west flash --hex-file <path_to_hex>
```

### Flashing Error Analysis

#### Common Flashing Errors and Solutions

**Device Not Found:**
- Error: `No connected devices found`
- Solution: Check USB connection, device drivers, and device permissions

**Permission Errors:**
- Error: `Permission denied`
- Solution: Add user to dialout group or run with sudo (not recommended)

**Connection Issues:**
- Error: `Failed to connect to target`
- Solution: Check device is in programming mode, reset device, check cables

**Flash Protection:**
- Error: `Flash is protected`
- Solution: Use `--erase` flag or disable flash protection

## 4. Device Log Reading

### Serial Port Discovery

#### nRF9151DK with J-Link OB Setup
```bash
# For nRF9151DK with J-Link On-Board debugger
# The device typically appears as:
# - macOS: /dev/tty.usbmodem<number> (e.g., /dev/tty.usbmodem1134202)
# - Linux: /dev/ttyACM<number>

# List available serial ports
ls /dev/tty.usbmodem* # macOS (J-Link CDC UART)
ls /dev/ttyACM* # Linux (J-Link CDC UART)

# Verify the specific port for your setup
ls -la /dev/tty.usbmodem1134202  # Your specific device

# Find device-specific ports with details
dmesg | grep tty # Linux
system_profiler SPUSBDataType | grep -A 10 "J-Link" # macOS
```

#### Linux/macOS General
```bash
# List all USB serial devices
ls /dev/tty.usb* # macOS
ls /dev/ttyUSB* /dev/ttyACM* # Linux

# Find device-specific ports
dmesg | grep tty # Linux
```

#### Windows
```cmd
# List COM ports
mode
```

### Log Capture Methods

#### Using Screen
```bash
# Start screen session with logging
screen -L -S <session_name> <serial_port> <baud_rate>

# Automated log capture
cd <project_directory> && \
rm -f screenlog.0 && \
screen -L -S <session_name> -d -m <serial_port> <baud_rate> && \
sleep <capture_duration> && \
screen -S <session_name> -X quit && \
cat screenlog.0
```

#### Using Minicom
```bash
# Start minicom with logging
minicom -D <serial_port> -b <baud_rate> -C <log_file>
```

#### Using PuTTY (Windows)
- Configure serial connection with logging enabled
- Set appropriate baud rate (typically 115200)

### Device Reset Commands

#### nRF Devices (nRF9151DK with J-Link)
```bash
# Reset using nrfutil (NCS v3.0.2)
nrfutil device reset --serial-number <serial_number>

# Reset using west with J-Link
west debug --reset

# Reset using J-Link Commander directly
JLinkExe -device nRF9151_xxAA -if SWD -speed 4000 -autoconnect 1 -CommanderScript reset.jlink

# Manual reset via hardware button on nRF9151DK
# Press RESET button on the development kit
```

#### Generic Reset
```bash
# Hardware reset (if reset pin available)
# Software reset through debugger
west attach --reset
```

## 5. Troubleshooting and Error Analysis

### Systematic Debugging Approach

#### Step 1: Build Analysis
1. Check build output for warnings and errors
2. Verify all required configurations are enabled
3. Ensure all dependencies are properly linked
4. Validate board and shield configurations

#### Step 2: Flash Analysis
1. Verify device connection and recognition
2. Check flash memory availability
3. Ensure proper flashing tool configuration
4. Validate firmware compatibility with target

#### Step 3: Runtime Log Analysis
1. Check for boot sequence completion
2. Identify initialization failures
3. Monitor for runtime errors and exceptions
4. Analyze performance and timing issues

### Common Runtime Issues

#### Boot Failures
- **Symptoms:** No output, partial boot sequence
- **Analysis:** Check power supply, clock configuration, memory settings
- **Solutions:** Verify board configuration, check hardware connections

#### Initialization Errors
- **Symptoms:** Specific subsystem failures during init
- **Analysis:** Look for error codes and failure points in logs
- **Solutions:** Check device tree, driver configurations, pin assignments

#### Memory Issues
- **Symptoms:** Stack overflow, heap exhaustion, hard faults
- **Analysis:** Monitor memory usage patterns in logs
- **Solutions:** Increase stack/heap sizes, optimize memory usage

#### Communication Failures
- **Symptoms:** Network, UART, SPI, I2C errors
- **Analysis:** Check protocol-specific error messages
- **Solutions:** Verify pin configurations, timing parameters, protocol settings

### Error Pattern Recognition

#### Critical Error Indicators
```
- "Hard fault" or "Bus fault"
- "Stack overflow"
- "Assertion failed"
- "Kernel panic"
- "Memory allocation failed"
```

#### Warning Indicators
```
- "Timeout"
- "Retry"
- "Configuration mismatch"
- "Deprecated"
```

## 6. Iterative Debugging Workflow

### Process Flow
1. **Attempt Build**
   - Run build command
   - If successful → proceed to flash
   - If failed → analyze build errors → apply fixes → retry build

2. **Attempt Flash**
   - Run flash command
   - If successful → proceed to log capture
   - If failed → analyze flash errors → apply fixes → retry flash

3. **Capture and Analyze Logs**
   - Reset device and capture logs
   - Analyze log output for errors or unexpected behavior
   - If issues found → apply fixes → return to build step
   - If successful → process complete

4. **Apply Fixes**
   - Based on error analysis, modify:
     - Source code
     - Configuration files
     - Device tree
     - Build settings
   - Document changes for future reference

### Automation Script Template
```bash
#!/bin/bash
# Automated Zephyr debugging script

PROJECT_DIR="<project_directory>"
BOARD="<board_name>"
SERIAL_PORT="<serial_port>"
BAUD_RATE="115200"
LOG_DURATION="10"

cd "$PROJECT_DIR"

# Build phase
echo "Building project..."
if ! west build -p -b "$BOARD"; then
    echo "Build failed. Check build output for errors."
    exit 1
fi

# Flash phase
echo "Flashing firmware..."
if ! west flash --erase; then
    echo "Flash failed. Check device connection."
    exit 1
fi

# Log capture phase
echo "Capturing device logs..."
rm -f screenlog.0
screen -L -S debug-session -d -m "$SERIAL_PORT" "$BAUD_RATE"
sleep "$LOG_DURATION"
screen -S debug-session -X quit

# Log analysis
echo "Device log output:"
cat screenlog.0

echo "Debugging cycle complete."
```

## 7. Best Practices

### Development Workflow
- Always use version control to track changes
- Test incrementally with small changes
- Keep detailed logs of debugging sessions
- Document successful configurations for reuse

### Error Prevention
- Validate configurations before building
- Use static analysis tools when available
- Implement proper error handling in code
- Regular testing on target hardware

### Performance Optimization
- Monitor resource usage during development
- Profile critical code paths
- Optimize for target hardware constraints
- Use appropriate compiler optimizations

## 8. Reference Examples

### Successful Boot Sequence Pattern
A typical successful Zephyr boot sequence should show:
``` 
*** Booting nRF Connect SDK v3.0.2-89ba1294ac9b ***
*** Using Zephyr OS v4.0.99-f791c49f492c ***
[timestamp] <inf> main: Application started
[timestamp] <inf> subsystem: Subsystem initialized
```

### Common Log Analysis Patterns
- Look for `<inf>` (info), `<wrn>` (warning), `<err>` (error) tags
- Monitor initialization sequence completion
- Check for assertion failures or kernel panics
- Verify expected application behavior markers

## 9. NCS nRF9151DK Specific Setup

### Hardware Configuration
- **Development Kit**: nRF9151DK with J-Link On-Board debugger
- **Target**: nRF9151 CPU App core (application core)
- **Network Core**: MFW 2.0.2 (Modem Firmware) pre-programmed
- **Bridge Firmware**: nRF5340 programmed with bridge firmware
- **Serial Port**: /dev/tty.usbmodem1134202 (115200 baud)

### NCS Project Build Commands
```bash
# Navigate to NCS workspace
cd /opt/nordic/ncs/v3.0.2

# Build cellular WiFi router for nRF9151DK
west build -p -b nrf9151dk/nrf9151/cpuapp nrf/applications/cellular_wifi_router

# Alternative: Build for Thingy:91 X
west build -p -b thingy91x/nrf9151/cpuapp nrf/applications/cellular_wifi_router

# Flash to nRF9151 CPU App core
west flash

# Monitor logs
screen -L -S debug /dev/tty.usbmodem1134202 115200
```

### Quick Debug Script for NCS nRF9151DK
```bash
#!/bin/bash
# NCS nRF9151DK Debug Script

NCS_ROOT="/opt/nordic/ncs/v3.0.2"
PROJECT_PATH="nrf/applications/cellular_wifi_router"
BOARD="nrf9151dk/nrf9151/cpuapp"
SERIAL_PORT="/dev/tty.usbmodem1134202"
BAUD_RATE="115200"

cd "$NCS_ROOT"

echo "=== NCS nRF9151DK Debugging Process ==="
echo "Building $PROJECT_PATH for $BOARD..."

if west build -p -b "$BOARD" "$PROJECT_PATH"; then
    echo "✓ Build successful"

    echo "Flashing to nRF9151 CPU App core..."
    if west flash; then
        echo "✓ Flash successful"

        echo "Starting log capture on $SERIAL_PORT..."
        echo "Press Ctrl+A then K to exit screen session"
        screen -L -S nrf9151-debug "$SERIAL_PORT" "$BAUD_RATE"
    else
        echo "✗ Flash failed"
        exit 1
    fi
else
    echo "✗ Build failed"
    exit 1
fi
```

This documentation provides a systematic approach to debugging Zephyr applications that can be adapted to any project and hardware platform, with specific optimizations for NCS and nRF9151DK development.
