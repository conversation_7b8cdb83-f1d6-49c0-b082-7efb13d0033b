#
# Copyright (c) 2021 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

# This sample uses Kconfig.defaults to set options common for all
# samples. This file should contain only options specific for this sample
# or overrides of default values.

# Enable CHIP
CONFIG_CHIP=y
CONFIG_CHIP_PROJECT_CONFIG="src/chip_project_config.h"
# 32781 == 0x800D (matter example of Temperature measurement device as NCS Weather Station)
CONFIG_CHIP_DEVICE_PRODUCT_ID=32781
CONFIG_STD_CPP17=y

# Enable Matter pairing automatically on application start.
CONFIG_CHIP_ENABLE_PAIRING_AUTOSTART=y

# Enable Matter extended announcement and increase duration to 1 hour.
CONFIG_CHIP_BLE_EXT_ADVERTISING=y
CONFIG_CHIP_BLE_ADVERTISING_DURATION=60

# Enable Bluetooth Low Energy
CONFIG_BT_DEVICE_NAME="MatterWeather"

# Add support for LEDs, buttons and buzzer
CONFIG_DK_LIBRARY=y
CONFIG_PWM=y


# Configure Thingy:53 sensors
CONFIG_I2C=y
CONFIG_SENSOR=y
CONFIG_BME680=y
CONFIG_ADC=y

# Suspend devices when the CPU goes into sleep
CONFIG_PM_DEVICE=y

# Average Thingy:53 current consumption
CONFIG_AVERAGE_CURRENT_CONSUMPTION=10000

# Enable DFU over Bluetooth LE SMP
CONFIG_CHIP_DFU_OVER_BT_SMP=y

# Increase system workqueue size, as SMP is processed within it
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=4096

# 0x0302 (Temp. sensor)
CONFIG_CHIP_DEVICE_TYPE=770
CONFIG_BT_RX_STACK_SIZE=1500

# Disable all debug features
CONFIG_SHELL=n
CONFIG_OPENTHREAD_SHELL=n
CONFIG_CONSOLE=n
CONFIG_UART_CONSOLE=n
CONFIG_SERIAL=n
CONFIG_LOG=n
CONFIG_LOG_MODE_MINIMAL=n
CONFIG_THREAD_NAME=n
CONFIG_ASSERT_VERBOSE=n
CONFIG_ASSERT_NO_FILE_INFO=y

# Disable USB CDC ACM
CONFIG_BOARD_SERIAL_BACKEND_CDC_ACM=n

# Enable Watchdog
CONFIG_NCS_SAMPLE_MATTER_WATCHDOG=y

# Enable LTO
CONFIG_LTO=y
CONFIG_ISR_TABLES_LOCAL_DECLARATION=y

# Enable Diagnostic Logs feature
CONFIG_NCS_SAMPLE_MATTER_DIAGNOSTIC_LOGS=y
CONFIG_NCS_SAMPLE_MATTER_DIAGNOSTIC_LOGS_CRASH_LOGS=y
CONFIG_NCS_SAMPLE_MATTER_DIAGNOSTIC_LOGS_REMOVE_CRASH_AFTER_READ=y
CONFIG_NCS_SAMPLE_MATTER_DIAGNOSTIC_LOGS_END_USER_LOGS=y
CONFIG_NCS_SAMPLE_MATTER_DIAGNOSTIC_LOGS_NETWORK_LOGS=y

# Enable test event triggers for diagnostic logs testing purposes.
CONFIG_NCS_SAMPLE_MATTER_DIAGNOSTIC_LOGS_TEST=y

# Set the NVS sector count to match the settings partition size that is 64 kB for this application.
CONFIG_SETTINGS_NVS_SECTOR_COUNT=16

CONFIG_NCS_SAMPLE_MATTER_LEDS=n