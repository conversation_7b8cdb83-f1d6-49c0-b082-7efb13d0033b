/*
 * Cellular WiFi Router - Utility Implementation
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include "utils.h"
#include <zephyr/logging/log.h>
#include <zephyr/net/net_mgmt.h>
#include <zephyr/net/wifi_mgmt.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/pwm.h>
#include <zephyr/sys/printk.h>
#include <modem/lte_lc.h>
#include <stdio.h>
#include <stdlib.h>

LOG_MODULE_REGISTER(utils, CONFIG_LOG_DEFAULT_LEVEL);

/* Static variables */
static button_callback_t button_cb = NULL;
static int64_t boot_time;

int utils_str_to_ip(const char *str, struct in_addr *addr)
{
	if (!str || !addr) {
		return -EINVAL;
	}
	
	return net_addr_pton(AF_INET, str, addr);
}

int utils_ip_to_str(const struct in_addr *addr, char *str, size_t str_len)
{
	if (!addr || !str || str_len < INET_ADDRSTRLEN) {
		return -EINVAL;
	}
	
	if (!net_addr_ntop(AF_INET, addr, str, str_len)) {
		return -EINVAL;
	}
	
	return 0;
}

int utils_mac_to_str(const uint8_t *mac, char *str, size_t str_len)
{
	if (!mac || !str || str_len < 18) {
		return -EINVAL;
	}
	
	snprintf(str, str_len, "%02x:%02x:%02x:%02x:%02x:%02x",
		 mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);
	
	return 0;
}

int utils_str_to_mac(const char *str, uint8_t *mac)
{
	if (!str || !mac) {
		return -EINVAL;
	}
	
	int ret = sscanf(str, "%02hhx:%02hhx:%02hhx:%02hhx:%02hhx:%02hhx",
			 &mac[0], &mac[1], &mac[2], &mac[3], &mac[4], &mac[5]);
	
	return (ret == 6) ? 0 : -EINVAL;
}

struct net_if *utils_get_cellular_iface(void)
{
	struct net_if *iface = NULL;
	
	/* Find the first PPP interface (cellular) */
	for (int i = 1; i <= NET_IF_MAX_CONFIGS; i++) {
		iface = net_if_get_by_index(i);
		if (iface && net_if_l2(iface) == &NET_L2_GET_NAME(PPP)) {
			return iface;
		}
	}
	
	return NULL;
}

struct net_if *utils_get_wifi_iface(void)
{
	return net_if_get_first_wifi();
}

bool utils_is_cellular_connected(void)
{
	enum lte_lc_nw_reg_status status;
	
	if (lte_lc_nw_reg_status_get(&status) == 0) {
		return (status == LTE_LC_NW_REG_REGISTERED_HOME ||
			status == LTE_LC_NW_REG_REGISTERED_ROAMING);
	}
	
	return false;
}

bool utils_is_wifi_ap_active(void)
{
	struct net_if *iface = utils_get_wifi_iface();
	
	if (!iface) {
		return false;
	}
	
	/* Check if interface is up and in AP mode */
	return net_if_is_up(iface);
}

int utils_get_signal_strength(void)
{
	/* TODO: Implement signal strength reading */
	/* The lte_lc_rsrp_get function is not available in this version */
	return -85; /* Return a dummy value for now */
}

uint32_t utils_get_uptime_seconds(void)
{
	return (uint32_t)((k_uptime_get() - boot_time) / 1000);
}

int utils_format_uptime(uint32_t seconds, char *str, size_t str_len)
{
	if (!str || str_len < 20) {
		return -EINVAL;
	}
	
	uint32_t days = seconds / 86400;
	uint32_t hours = (seconds % 86400) / 3600;
	uint32_t mins = (seconds % 3600) / 60;
	uint32_t secs = seconds % 60;
	
	if (days > 0) {
		snprintf(str, str_len, "%ud %02u:%02u:%02u", days, hours, mins, secs);
	} else {
		snprintf(str, str_len, "%02u:%02u:%02u", hours, mins, secs);
	}
	
	return 0;
}

void utils_print_memory_usage(void)
{
	struct k_mem_slab_info slab_info;
	size_t free_heap = utils_get_free_heap();
	
	LOG_INF("Memory usage:");
	LOG_INF("  Free heap: %zu bytes", free_heap);
	
#ifdef CONFIG_KERNEL_MEM_POOL
	LOG_INF("  Kernel memory pool usage available");
#endif
}

size_t utils_get_free_heap(void)
{
#ifdef CONFIG_HEAP_MEM_POOL_SIZE
	/* This is a simplified implementation */
	/* In a real implementation, you would query the actual heap usage */
	return CONFIG_HEAP_MEM_POOL_SIZE;
#else
	return 0;
#endif
}

const char *utils_error_to_string(int error)
{
	switch (error) {
	case 0:
		return "Success";
	case -EINVAL:
		return "Invalid argument";
	case -ENOMEM:
		return "Out of memory";
	case -ENODEV:
		return "No such device";
	case -EBUSY:
		return "Device busy";
	case -ETIMEDOUT:
		return "Timeout";
	case -ECONNREFUSED:
		return "Connection refused";
	case -ENETDOWN:
		return "Network down";
	case -ENETUNREACH:
		return "Network unreachable";
	default:
		return "Unknown error";
	}
}

void utils_log_error(const char *module, const char *function, int error)
{
	LOG_ERR("%s:%s() failed with error %d (%s)", 
		module, function, error, utils_error_to_string(error));
}

void utils_hexdump(const void *data, size_t len, const char *prefix)
{
	const uint8_t *bytes = (const uint8_t *)data;

	if (!data || len == 0) {
		return;
	}

	for (size_t i = 0; i < len; i += 16) {
		printk("%s%04zx: ", prefix ? prefix : "", i);

		/* Print hex bytes */
		for (size_t j = 0; j < 16 && (i + j) < len; j++) {
			printk("%02x ", bytes[i + j]);
		}

		/* Pad if necessary */
		for (size_t j = len - i; j < 16; j++) {
			printk("   ");
		}

		printk(" |");

		/* Print ASCII representation */
		for (size_t j = 0; j < 16 && (i + j) < len; j++) {
			uint8_t c = bytes[i + j];
			printk("%c", (c >= 32 && c <= 126) ? c : '.');
		}

		printk("|\n");
	}
}

void utils_print_network_stats(void)
{
	LOG_INF("Network statistics:");
	LOG_INF("  Cellular interface: %s", utils_is_cellular_connected() ? "Connected" : "Disconnected");
	LOG_INF("  WiFi AP: %s", utils_is_wifi_ap_active() ? "Active" : "Inactive");
	LOG_INF("  Signal strength: %d dBm", utils_get_signal_strength());
}

void utils_print_system_info(void)
{
	LOG_INF("System information:");
	LOG_INF("  Uptime: %u seconds", utils_get_uptime_seconds());
	LOG_INF("  Free heap: %zu bytes", utils_get_free_heap());
}
