/*
 * Cellular WiFi Router - Event Definitions
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef EVENTS_H
#define EVENTS_H

#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/wifi_mgmt.h>
#include <modem/lte_lc.h>

#ifdef __cplusplus
extern "C" {
#endif

/* System Events */
enum system_event_type {
	SYSTEM_EVENT_INIT,
	SYSTEM_EVENT_READY,
	SYSTEM_EVENT_ERROR,
	SYSTEM_EVENT_SHUTDOWN,
	SYSTEM_EVENT_LOW_POWER,
	SYSTEM_EVENT_CRITICAL_POWER,
};

struct system_event {
	enum system_event_type type;
	int error_code;
	char message[64];
};

/* Cellular Events */
enum cellular_event_type {
	CELLULAR_EVENT_CONNECTING,
	CELLULAR_EVENT_CONNECTED,
	CELLULAR_EVENT_DISCONNECTED,
	CELLULAR_EVENT_ERROR,
	CELLULAR_EVENT_DATA_READY,
	CELLULAR_EVENT_SIGNAL_UPDATE,
};

struct cellular_event {
	enum cellular_event_type type;
	enum lte_lc_nw_reg_status reg_status;
	enum lte_lc_lte_mode lte_mode;
	int rsrp;
	int rsrq;
	int error_code;
	struct net_if *iface;
};

/* WiFi Events */
enum wifi_event_type {
	WIFI_EVENT_AP_STARTING,
	WIFI_EVENT_AP_STARTED,
	WIFI_EVENT_AP_STOPPED,
	WIFI_EVENT_AP_ERROR,
	WIFI_EVENT_CLIENT_CONNECTED,
	WIFI_EVENT_CLIENT_DISCONNECTED,
	WIFI_EVENT_SCAN_COMPLETE,
};

struct wifi_event {
	enum wifi_event_type type;
	struct net_if *iface;
	uint8_t client_mac[6];
	int client_count;
	int error_code;
	struct wifi_scan_result scan_results[10];
	int scan_count;
};

/* Bridge Events */
enum bridge_event_type {
	BRIDGE_EVENT_STARTING,
	BRIDGE_EVENT_STARTED,
	BRIDGE_EVENT_STOPPED,
	BRIDGE_EVENT_ERROR,
	BRIDGE_EVENT_PACKET_FORWARDED,
	BRIDGE_EVENT_NAT_ENTRY_ADDED,
	BRIDGE_EVENT_NAT_ENTRY_REMOVED,
};

struct bridge_event {
	enum bridge_event_type type;
	struct net_if *cellular_iface;
	struct net_if *wifi_iface;
	uint32_t packets_forwarded;
	uint32_t bytes_forwarded;
	int error_code;
};

/* UI Events */
enum ui_event_type {
	UI_EVENT_BUTTON_PRESSED,
	UI_EVENT_BUTTON_RELEASED,
	UI_EVENT_LED_UPDATE,
	UI_EVENT_DISPLAY_UPDATE,
	UI_EVENT_UI_DISABLED,
	UI_EVENT_UI_ENABLED,
};

struct ui_event {
	enum ui_event_type type;
	int button_id;
	int led_id;
	bool led_state;
	uint8_t led_brightness;
	char display_text[32];
};

/* Power Events */
enum power_event_type {
	POWER_EVENT_BATTERY_UPDATE,
	POWER_EVENT_LOW_BATTERY,
	POWER_EVENT_CRITICAL_BATTERY,
	POWER_EVENT_CHARGING_STARTED,
	POWER_EVENT_CHARGING_STOPPED,
	POWER_EVENT_SLEEP_REQUEST,
	POWER_EVENT_WAKE_REQUEST,
};

struct power_event {
	enum power_event_type type;
	uint16_t battery_voltage_mv;
	uint8_t battery_percentage;
	bool charging;
	bool usb_connected;
	int requested_state;
};

/* Network Statistics */
struct network_stats {
	uint32_t cellular_rx_bytes;
	uint32_t cellular_tx_bytes;
	uint32_t wifi_rx_bytes;
	uint32_t wifi_tx_bytes;
	uint32_t packets_forwarded;
	uint32_t packets_dropped;
	uint32_t nat_entries_active;
	uint32_t dhcp_leases_active;
	uint32_t uptime_seconds;
};

/* Configuration Structure */
struct router_config {
	/* WiFi AP Configuration */
	char wifi_ssid[32];
	char wifi_password[64];
	uint8_t wifi_channel;
	uint8_t wifi_max_clients;
	
	/* Network Configuration */
	char dhcp_pool_start[16];
	char dhcp_pool_end[16];
	char dhcp_gateway[16];
	char dhcp_netmask[16];
	
	/* Power Configuration */
	uint16_t battery_low_threshold_mv;
	uint16_t battery_critical_threshold_mv;
	uint32_t cellular_idle_timeout_sec;
	uint32_t wifi_idle_timeout_sec;
	bool deep_sleep_enable;
	
	/* UI Configuration */
	bool ui_enabled;
	uint8_t led_brightness;
	bool button_enabled;
};

/* Function declarations */
int events_init(void);
int events_publish_system(const struct system_event *event);
int events_publish_cellular(const struct cellular_event *event);
int events_publish_wifi(const struct wifi_event *event);
int events_publish_bridge(const struct bridge_event *event);
int events_publish_ui(const struct ui_event *event);
int events_publish_power(const struct power_event *event);

#ifdef __cplusplus
}
#endif

#endif /* EVENTS_H */
