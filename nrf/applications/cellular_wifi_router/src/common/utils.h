/*
 * Cellular WiFi Router - Utility Functions
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef UTILS_H
#define UTILS_H

#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_ip.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Utility macros - use Zephyr's built-in versions */

/* String utilities */
int utils_str_to_ip(const char *str, struct in_addr *addr);
int utils_ip_to_str(const struct in_addr *addr, char *str, size_t str_len);
int utils_mac_to_str(const uint8_t *mac, char *str, size_t str_len);
int utils_str_to_mac(const char *str, uint8_t *mac);

/* Network utilities */
struct net_if *utils_get_cellular_iface(void);
struct net_if *utils_get_wifi_iface(void);
bool utils_is_cellular_connected(void);
bool utils_is_wifi_ap_active(void);
int utils_get_signal_strength(void);

/* Time utilities */
uint32_t utils_get_uptime_seconds(void);
int utils_format_uptime(uint32_t seconds, char *str, size_t str_len);

/* Memory utilities */
void utils_print_memory_usage(void);
size_t utils_get_free_heap(void);

/* Configuration utilities */
int utils_load_config(void);
int utils_save_config(void);
int utils_reset_config_to_defaults(void);

/* LED control utilities */
int utils_set_led_pattern(int led_id, int pattern);
int utils_set_led_brightness(int led_id, uint8_t brightness);

/* Button utilities */
typedef void (*button_callback_t)(int button_id, bool pressed);
int utils_register_button_callback(button_callback_t callback);

/* Power utilities */
int utils_get_battery_voltage(void);
int utils_get_battery_percentage(void);
bool utils_is_charging(void);
bool utils_is_usb_connected(void);

/* Debug utilities */
void utils_print_network_stats(void);
void utils_print_system_info(void);
void utils_hexdump(const void *data, size_t len, const char *prefix);

/* Error handling utilities */
const char *utils_error_to_string(int error);
void utils_log_error(const char *module, const char *function, int error);

#ifdef __cplusplus
}
#endif

#endif /* UTILS_H */
