/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef ROUTER_EVENTS_H_
#define ROUTER_EVENTS_H_

#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Cellular module events
 */
enum cellular_event_type {
	CELLULAR_EVENT_DISCONNECTED,
	CELLULAR_EVENT_CONNECTING,
	CELLULAR_EVENT_CONNECTED,
	CELLULAR_EVENT_ERROR,
	CELLULAR_EVENT_DATA_READY,
};

struct cellular_event {
	enum cellular_event_type type;
	int error_code;
	struct net_if *iface;
	char operator_name[32];
	int signal_strength;
};

/**
 * @brief WiFi module events
 */
enum wifi_event_type {
	WIFI_EVENT_AP_DISABLED,
	WIFI_EVENT_AP_ENABLING,
	WIFI_EVENT_AP_ENABLED,
	WIFI_EVENT_AP_ERROR,
	WIFI_EVENT_CLIENT_CONNECTED,
	WIFI_EVENT_CLIENT_DISCONNECTED,
};

struct wifi_event {
	enum wifi_event_type type;
	int error_code;
	struct net_if *iface;
	uint8_t client_mac[6];
	int client_count;
	char ssid[32];
};

/**
 * @brief Bridge module events
 */
enum bridge_event_type {
	BRIDGE_EVENT_DISABLED,
	BRIDGE_EVENT_ENABLING,
	BRIDGE_EVENT_ENABLED,
	BRIDGE_EVENT_ERROR,
	BRIDGE_EVENT_PACKET_FORWARDED,
	BRIDGE_EVENT_NAT_ENTRY_ADDED,
	BRIDGE_EVENT_NAT_ENTRY_REMOVED,
};

struct bridge_event {
	enum bridge_event_type type;
	int error_code;
	uint32_t packets_forwarded;
	uint32_t bytes_forwarded;
	uint16_t nat_entries_count;
};

/**
 * @brief UI module events
 */
enum ui_event_type {
	UI_EVENT_BUTTON_PRESSED,
	UI_EVENT_BUTTON_RELEASED,
	UI_EVENT_LONG_PRESS,
	UI_EVENT_LED_STATE_CHANGED,
};

struct ui_event {
	enum ui_event_type type;
	int button_id;
	int led_id;
	bool led_state;
	uint32_t press_duration_ms;
};

/**
 * @brief Power module events
 */
enum power_event_type {
	POWER_EVENT_BATTERY_LOW,
	POWER_EVENT_BATTERY_CRITICAL,
	POWER_EVENT_CHARGING_STARTED,
	POWER_EVENT_CHARGING_STOPPED,
	POWER_EVENT_POWER_SAVE_ENTERED,
	POWER_EVENT_POWER_SAVE_EXITED,
};

struct power_event {
	enum power_event_type type;
	int battery_level_percent;
	bool charging;
	bool power_save_active;
};

/**
 * @brief System events
 */
enum system_event_type {
	SYSTEM_EVENT_STARTUP,
	SYSTEM_EVENT_SHUTDOWN,
	SYSTEM_EVENT_RESET_REQUESTED,
	SYSTEM_EVENT_CONFIG_MODE_ENTERED,
	SYSTEM_EVENT_CONFIG_MODE_EXITED,
	SYSTEM_EVENT_ERROR,
};

struct system_event {
	enum system_event_type type;
	int error_code;
	char error_message[64];
};

/* ZBUS channel declarations */
ZBUS_CHAN_DECLARE(cellular_chan);
ZBUS_CHAN_DECLARE(wifi_chan);
ZBUS_CHAN_DECLARE(bridge_chan);
ZBUS_CHAN_DECLARE(ui_chan);
ZBUS_CHAN_DECLARE(power_chan);
ZBUS_CHAN_DECLARE(system_chan);

#ifdef __cplusplus
}
#endif

#endif /* ROUTER_EVENTS_H_ */
