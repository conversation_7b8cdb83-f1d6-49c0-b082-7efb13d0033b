/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/kernel.h>
#include <zephyr/zbus/zbus.h>
#include "router_events.h"

#include <zephyr/logging/log.h>
LOG_MODULE_REGISTER(router_events, CONFIG_LOG_DEFAULT_LEVEL);

/* ZBUS channel definitions */
ZBUS_CHAN_DEFINE(cellular_chan,
		 struct cellular_event,
		 NULL,
		 NULL,
		 ZBUS_OBSERVERS_EMPTY,
		 ZBUS_MSG_INIT(.type = CELLULAR_EVENT_DISCONNECTED)
);

ZBUS_CHAN_DEFINE(wifi_chan,
		 struct wifi_event,
		 NULL,
		 NULL,
		 ZBUS_OBSERVERS_EMPTY,
		 ZBUS_MSG_INIT(.type = WIFI_EVENT_AP_DISABLED)
);

ZBUS_CHAN_DEFINE(bridge_chan,
		 struct bridge_event,
		 NULL,
		 NULL,
		 ZBUS_OBSERVERS_EMPTY,
		 ZBUS_MSG_INIT(.type = BRIDGE_EVENT_DISABLED)
);

ZBUS_CHAN_DEFINE(ui_chan,
		 struct ui_event,
		 NULL,
		 NULL,
		 ZBUS_OBSERVERS_EMPTY,
		 ZBUS_MSG_INIT(.type = UI_EVENT_BUTTON_RELEASED)
);

ZBUS_CHAN_DEFINE(power_chan,
		 struct power_event,
		 NULL,
		 NULL,
		 ZBUS_OBSERVERS_EMPTY,
		 ZBUS_MSG_INIT(.type = POWER_EVENT_BATTERY_LOW, .battery_level_percent = 100)
);

ZBUS_CHAN_DEFINE(system_chan,
		 struct system_event,
		 NULL,
		 NULL,
		 ZBUS_OBSERVERS_EMPTY,
		 ZBUS_MSG_INIT(.type = SYSTEM_EVENT_STARTUP)
);
