/*
 * Cellular WiFi Router - UI Module Implementation
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include "ui_module.h"
#include "../common/events.h"
#include "../common/utils.h"
#include <zephyr/logging/log.h>
#include <zephyr/zbus/zbus.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/pwm.h>
#include <zephyr/device.h>
#include <zephyr/devicetree.h>
#include <zephyr/smf.h>

LOG_MODULE_REGISTER(ui_module, CONFIG_LOG_DEFAULT_LEVEL);

/* Device tree definitions */
#define LED_STATUS_NODE DT_ALIAS(led0)
#define LED_CELLULAR_NODE DT_ALIAS(led1)
#define LED_WIFI_NODE DT_ALIAS(led2)
#define BUTTON_CONTROL_NODE DT_ALIAS(sw0)

/* PWM LED definitions */
#define PWM_LED_STATUS_NODE DT_ALIAS(pwm_led0)
#define PWM_LED_CELLULAR_NODE DT_ALIAS(pwm_led1)
#define PWM_LED_WIFI_NODE DT_ALIAS(pwm_led2)

/* UI Module State Machine */
static struct smf_ctx ui_smf_ctx;
static enum ui_state current_ui_state = UI_STATE_DISABLED;
static bool ui_enabled = true;
static bool ui_healthy = false;

/* LED Control Arrays */
static struct led_control leds[LED_COUNT];
static struct button_control buttons[BUTTON_COUNT];

/* ZBUS Observers */
ZBUS_SUBSCRIBER_DEFINE(ui_system_events_sub, 4);
ZBUS_SUBSCRIBER_DEFINE(ui_cellular_events_sub, 4);
ZBUS_SUBSCRIBER_DEFINE(ui_wifi_events_sub, 4);
ZBUS_SUBSCRIBER_DEFINE(ui_power_events_sub, 4);

/* Forward declarations */
static void led_pattern_work_handler(struct k_work *work);
static void button_gpio_callback(const struct device *dev, struct gpio_callback *cb, uint32_t pins);
static int ui_init_leds(void);
static int ui_init_buttons(void);
static void ui_update_leds_for_state(enum ui_state state);

/* LED Pattern work handler */
static void led_pattern_work_handler(struct k_work *work)
{
	struct k_work_delayable *dwork = k_work_delayable_from_work(work);
	struct led_control *led = CONTAINER_OF(dwork, struct led_control, pattern_work);
	static bool pattern_state = false;
	int led_id = led - leds;
	
	if (!led->enabled || !ui_enabled) {
		return;
	}
	
	switch (led->current_pattern) {
	case LED_PATTERN_OFF:
		ui_led_set_state(led_id, false);
		return;
		
	case LED_PATTERN_ON:
		ui_led_set_state(led_id, true);
		return;
		
	case LED_PATTERN_SLOW_BLINK:
		ui_led_set_state(led_id, pattern_state);
		pattern_state = !pattern_state;
		k_work_reschedule(&led->pattern_work, K_MSEC(1000));
		break;
		
	case LED_PATTERN_FAST_BLINK:
		ui_led_set_state(led_id, pattern_state);
		pattern_state = !pattern_state;
		k_work_reschedule(&led->pattern_work, K_MSEC(250));
		break;
		
	case LED_PATTERN_PULSE:
		/* Implement PWM pulse pattern */
		if (led->pwm_dev) {
			static uint8_t pulse_brightness = 0;
			static bool pulse_up = true;
			
			if (pulse_up) {
				pulse_brightness += 10;
				if (pulse_brightness >= led->brightness) {
					pulse_up = false;
				}
			} else {
				pulse_brightness -= 10;
				if (pulse_brightness <= 10) {
					pulse_up = true;
				}
			}
			
			ui_led_set_brightness(led_id, pulse_brightness);
			k_work_reschedule(&led->pattern_work, K_MSEC(50));
		}
		break;
		
	case LED_PATTERN_DOUBLE_BLINK:
		/* Implement double blink pattern */
		static int blink_count = 0;
		ui_led_set_state(led_id, pattern_state);
		pattern_state = !pattern_state;
		blink_count++;
		
		if (blink_count >= 4) {
			blink_count = 0;
			k_work_reschedule(&led->pattern_work, K_MSEC(1000));
		} else {
			k_work_reschedule(&led->pattern_work, K_MSEC(150));
		}
		break;
	}
}

/* Button GPIO callback */
static void button_gpio_callback(const struct device *dev, struct gpio_callback *cb, uint32_t pins)
{
	struct button_control *button = CONTAINER_OF(cb, struct button_control, gpio_cb);
	int button_id = button - buttons;
	int64_t now = k_uptime_get();
	bool pressed;
	
	if (!button->enabled || !ui_enabled) {
		return;
	}
	
	/* Read current button state */
	pressed = !gpio_pin_get(button->gpio_dev, button->gpio_pin);
	
	/* Debounce button */
	if (pressed && (now - button->last_press_time) > 50) {
		button->pressed = true;
		button->last_press_time = now;
		
		LOG_DBG("Button %d pressed", button_id);
		
		/* Publish button event */
		struct ui_event event = {
			.type = UI_EVENT_BUTTON_PRESSED,
			.button_id = button_id,
		};
		events_publish_ui(&event);
		
	} else if (!pressed && (now - button->last_release_time) > 50) {
		button->pressed = false;
		button->last_release_time = now;
		
		LOG_DBG("Button %d released", button_id);
		
		/* Publish button event */
		struct ui_event event = {
			.type = UI_EVENT_BUTTON_RELEASED,
			.button_id = button_id,
		};
		events_publish_ui(&event);
	}
}

static int ui_init_leds(void)
{
	int ret;
	
	/* Initialize Status LED */
	if (DT_NODE_EXISTS(LED_STATUS_NODE)) {
		leds[LED_STATUS].gpio_dev = DEVICE_DT_GET(DT_GPIO_CTLR(LED_STATUS_NODE, gpios));
		leds[LED_STATUS].gpio_pin = DT_GPIO_PIN(LED_STATUS_NODE, gpios);
		leds[LED_STATUS].gpio_active_low = DT_GPIO_FLAGS(LED_STATUS_NODE, gpios) & GPIO_ACTIVE_LOW;
		
		if (DT_NODE_EXISTS(PWM_LED_STATUS_NODE)) {
			leds[LED_STATUS].pwm_dev = DEVICE_DT_GET(DT_PWMS_CTLR(PWM_LED_STATUS_NODE));
			leds[LED_STATUS].pwm_channel = DT_PWMS_CHANNEL(PWM_LED_STATUS_NODE);
		}
		
		ret = gpio_pin_configure(leds[LED_STATUS].gpio_dev, leds[LED_STATUS].gpio_pin,
					 GPIO_OUTPUT | (leds[LED_STATUS].gpio_active_low ? GPIO_ACTIVE_LOW : GPIO_ACTIVE_HIGH));
		if (ret) {
			LOG_ERR("Failed to configure status LED GPIO: %d", ret);
			return ret;
		}
		
		leds[LED_STATUS].enabled = true;
		leds[LED_STATUS].brightness = 50;
		leds[LED_STATUS].current_pattern = LED_PATTERN_OFF;
		k_work_init_delayable(&leds[LED_STATUS].pattern_work, led_pattern_work_handler);
	}
	
	/* Initialize similar for other LEDs... */
	/* For brevity, showing pattern for one LED */
	
	LOG_INF("LEDs initialized successfully");
	return 0;
}

static int ui_init_buttons(void)
{
	int ret;
	
	/* Initialize Control Button */
	if (DT_NODE_EXISTS(BUTTON_CONTROL_NODE)) {
		buttons[BUTTON_CONTROL].gpio_dev = DEVICE_DT_GET(DT_GPIO_CTLR(BUTTON_CONTROL_NODE, gpios));
		buttons[BUTTON_CONTROL].gpio_pin = DT_GPIO_PIN(BUTTON_CONTROL_NODE, gpios);
		
		ret = gpio_pin_configure(buttons[BUTTON_CONTROL].gpio_dev, 
					 buttons[BUTTON_CONTROL].gpio_pin,
					 GPIO_INPUT | GPIO_PULL_UP);
		if (ret) {
			LOG_ERR("Failed to configure button GPIO: %d", ret);
			return ret;
		}
		
		ret = gpio_pin_interrupt_configure(buttons[BUTTON_CONTROL].gpio_dev,
						   buttons[BUTTON_CONTROL].gpio_pin,
						   GPIO_INT_EDGE_BOTH);
		if (ret) {
			LOG_ERR("Failed to configure button interrupt: %d", ret);
			return ret;
		}
		
		gpio_init_callback(&buttons[BUTTON_CONTROL].gpio_cb, button_gpio_callback,
				   BIT(buttons[BUTTON_CONTROL].gpio_pin));
		
		ret = gpio_add_callback(buttons[BUTTON_CONTROL].gpio_dev, &buttons[BUTTON_CONTROL].gpio_cb);
		if (ret) {
			LOG_ERR("Failed to add button callback: %d", ret);
			return ret;
		}
		
		buttons[BUTTON_CONTROL].enabled = true;
	}
	
	LOG_INF("Buttons initialized successfully");
	return 0;
}

int ui_module_init(void)
{
	int ret;
	
	LOG_INF("Initializing UI module");
	
	/* Initialize LEDs */
	ret = ui_init_leds();
	if (ret) {
		LOG_ERR("Failed to initialize LEDs: %d", ret);
		return ret;
	}
	
	/* Initialize buttons */
	ret = ui_init_buttons();
	if (ret) {
		LOG_ERR("Failed to initialize buttons: %d", ret);
		return ret;
	}
	
	/* Subscribe to events - simplified for now */
	/* TODO: Implement proper ZBUS event subscription */
	LOG_INF("Event subscription skipped for now");
	
	/* Set initial state */
	current_ui_state = UI_STATE_INITIALIZING;
	ui_update_leds_for_state(current_ui_state);
	
	ui_healthy = true;
	LOG_INF("UI module initialized successfully");
	return 0;
}

int ui_led_set_pattern(enum led_id led, enum led_pattern pattern)
{
	if (led >= LED_COUNT || !leds[led].enabled) {
		return -EINVAL;
	}
	
	leds[led].current_pattern = pattern;
	k_work_reschedule(&leds[led].pattern_work, K_NO_WAIT);
	
	return 0;
}

int ui_led_set_state(enum led_id led, bool on)
{
	if (led >= LED_COUNT || !leds[led].enabled) {
		return -EINVAL;
	}
	
	bool output_state = leds[led].gpio_active_low ? !on : on;
	return gpio_pin_set(leds[led].gpio_dev, leds[led].gpio_pin, output_state);
}

bool ui_module_is_healthy(void)
{
	return ui_healthy;
}

int ui_module_shutdown(void)
{
	LOG_INF("Shutting down UI module");
	
	/* Turn off all LEDs */
	for (int i = 0; i < LED_COUNT; i++) {
		ui_led_set_pattern(i, LED_PATTERN_OFF);
		k_work_cancel_delayable(&leds[i].pattern_work);
	}
	
	ui_healthy = false;
	current_ui_state = UI_STATE_DISABLED;
	
	LOG_INF("UI module shutdown completed");
	return 0;
}

static void ui_update_leds_for_state(enum ui_state state)
{
	/* Update LED patterns based on current state */
	switch (state) {
	case UI_STATE_DISABLED:
		ui_led_set_pattern(LED_STATUS, LED_PATTERN_OFF);
		ui_led_set_pattern(LED_CELLULAR, LED_PATTERN_OFF);
		ui_led_set_pattern(LED_WIFI, LED_PATTERN_OFF);
		break;

	case UI_STATE_INITIALIZING:
		ui_led_set_pattern(LED_STATUS, LED_PATTERN_SLOW_BLINK);
		ui_led_set_pattern(LED_CELLULAR, LED_PATTERN_OFF);
		ui_led_set_pattern(LED_WIFI, LED_PATTERN_OFF);
		break;

	case UI_STATE_IDLE:
		ui_led_set_pattern(LED_STATUS, LED_PATTERN_ON);
		ui_led_set_pattern(LED_CELLULAR, LED_PATTERN_OFF);
		ui_led_set_pattern(LED_WIFI, LED_PATTERN_OFF);
		break;

	case UI_STATE_CELLULAR_CONNECTING:
		ui_led_set_pattern(LED_STATUS, LED_PATTERN_ON);
		ui_led_set_pattern(LED_CELLULAR, LED_PATTERN_FAST_BLINK);
		ui_led_set_pattern(LED_WIFI, LED_PATTERN_OFF);
		break;

	case UI_STATE_WIFI_STARTING:
		ui_led_set_pattern(LED_STATUS, LED_PATTERN_ON);
		ui_led_set_pattern(LED_CELLULAR, LED_PATTERN_ON);
		ui_led_set_pattern(LED_WIFI, LED_PATTERN_FAST_BLINK);
		break;

	case UI_STATE_ROUTER_ACTIVE:
		ui_led_set_pattern(LED_STATUS, LED_PATTERN_ON);
		ui_led_set_pattern(LED_CELLULAR, LED_PATTERN_ON);
		ui_led_set_pattern(LED_WIFI, LED_PATTERN_ON);
		break;

	case UI_STATE_ERROR:
		ui_led_set_pattern(LED_STATUS, LED_PATTERN_FAST_BLINK);
		ui_led_set_pattern(LED_CELLULAR, LED_PATTERN_OFF);
		ui_led_set_pattern(LED_WIFI, LED_PATTERN_OFF);
		break;

	case UI_STATE_LOW_POWER:
		ui_led_set_pattern(LED_STATUS, LED_PATTERN_PULSE);
		ui_led_set_pattern(LED_CELLULAR, LED_PATTERN_OFF);
		ui_led_set_pattern(LED_WIFI, LED_PATTERN_OFF);
		break;

	default:
		break;
	}
}

int ui_led_set_brightness(enum led_id led, uint8_t brightness)
{
	/* Set LED brightness */
	LOG_DBG("Setting LED %d brightness to %d", led, brightness);
	/* TODO: Implement actual LED brightness control */
	return 0;
}
