/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/pwm.h>
#include <zephyr/zbus/zbus.h>

#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED
#include <dk_buttons_and_leds.h>
#endif

#include "ui_module.h"
#include "../events/router_events.h"

LOG_MODULE_REGISTER(ui_module, CONFIG_LOG_DEFAULT_LEVEL);

#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED

/* UI context */
struct ui_ctx {
	bool enabled;
	struct k_work_delayable led_work[4];
	enum led_state led_states[4];
	struct k_work button_work;
	struct gpio_callback button_cb_data;
	uint32_t button_press_time[2];
	bool button_pressed[2];
	struct k_timer long_press_timer[2];
};

static struct ui_ctx ui_ctx;

/* LED blink patterns */
#define BLINK_SLOW_PERIOD_MS    1000
#define BLINK_FAST_PERIOD_MS    200
#define PULSE_PERIOD_MS         2000
#define LONG_PRESS_TIMEOUT_MS   3000

/* LED work handlers */
static void led_work_handler(struct k_work *work)
{
	struct k_work_delayable *dwork = k_work_delayable_from_work(work);
	int led_idx = dwork - ui_ctx.led_work;
	
	if (!ui_ctx.enabled || led_idx >= 4) {
		return;
	}
	
	switch (ui_ctx.led_states[led_idx]) {
	case LED_STATE_BLINK_SLOW:
		dk_set_led(led_idx, !dk_get_led_status(led_idx));
		k_work_schedule(dwork, K_MSEC(BLINK_SLOW_PERIOD_MS / 2));
		break;
		
	case LED_STATE_BLINK_FAST:
		dk_set_led(led_idx, !dk_get_led_status(led_idx));
		k_work_schedule(dwork, K_MSEC(BLINK_FAST_PERIOD_MS / 2));
		break;
		
	case LED_STATE_PULSE:
		/* Simple pulse implementation - could be enhanced with PWM */
		dk_set_led(led_idx, 1);
		k_work_schedule(dwork, K_MSEC(100));
		break;
		
	default:
		/* Stop blinking for other states */
		break;
	}
}

/* Button callback */
static void button_pressed(const struct device *dev, struct gpio_callback *cb,
			  uint32_t pins)
{
	if (!ui_ctx.enabled) {
		return;
	}
	
	k_work_submit(&ui_ctx.button_work);
}

/* Button work handler */
static void button_work_handler(struct k_work *work)
{
	uint32_t button_state = dk_get_buttons();
	uint32_t current_time = k_uptime_get_32();
	
	for (int i = 0; i < 2; i++) {
		bool pressed = (button_state & BIT(i)) != 0;
		
		if (pressed && !ui_ctx.button_pressed[i]) {
			/* Button press detected */
			ui_ctx.button_pressed[i] = true;
			ui_ctx.button_press_time[i] = current_time;
			
			/* Start long press timer */
			k_timer_start(&ui_ctx.long_press_timer[i], 
				     K_MSEC(LONG_PRESS_TIMEOUT_MS), K_NO_WAIT);
			
			struct ui_event evt = {
				.type = UI_EVENT_BUTTON_PRESSED,
				.button_id = i
			};
			zbus_chan_pub(&ui_chan, &evt, K_MSEC(100));
			
		} else if (!pressed && ui_ctx.button_pressed[i]) {
			/* Button release detected */
			ui_ctx.button_pressed[i] = false;
			uint32_t press_duration = current_time - ui_ctx.button_press_time[i];
			
			/* Stop long press timer */
			k_timer_stop(&ui_ctx.long_press_timer[i]);
			
			struct ui_event evt = {
				.type = UI_EVENT_BUTTON_RELEASED,
				.button_id = i,
				.press_duration_ms = press_duration
			};
			zbus_chan_pub(&ui_chan, &evt, K_MSEC(100));
		}
	}
}

/* Long press timer handlers */
static void long_press_timer_handler(struct k_timer *timer)
{
	int button_idx = timer - ui_ctx.long_press_timer;
	
	if (button_idx >= 0 && button_idx < 2 && ui_ctx.button_pressed[button_idx]) {
		struct ui_event evt = {
			.type = UI_EVENT_LONG_PRESS,
			.button_id = button_idx,
			.press_duration_ms = LONG_PRESS_TIMEOUT_MS
		};
		zbus_chan_pub(&ui_chan, &evt, K_MSEC(100));
	}
}

/* ZBUS observers for automatic LED control */
static void cellular_event_observer(const struct zbus_channel *chan)
{
	const struct cellular_event *evt = zbus_chan_const_msg(chan);
	
	switch (evt->type) {
	case CELLULAR_EVENT_DISCONNECTED:
		ui_module_set_led(LED_CELLULAR, LED_STATE_OFF);
		break;
	case CELLULAR_EVENT_CONNECTING:
		ui_module_set_led(LED_CELLULAR, LED_STATE_BLINK_FAST);
		break;
	case CELLULAR_EVENT_CONNECTED:
		ui_module_set_led(LED_CELLULAR, LED_STATE_ON);
		break;
	case CELLULAR_EVENT_ERROR:
		ui_module_set_led(LED_CELLULAR, LED_STATE_BLINK_SLOW);
		break;
	default:
		break;
	}
}

static void wifi_event_observer(const struct zbus_channel *chan)
{
	const struct wifi_event *evt = zbus_chan_const_msg(chan);
	
	switch (evt->type) {
	case WIFI_EVENT_AP_DISABLED:
		ui_module_set_led(LED_WIFI, LED_STATE_OFF);
		break;
	case WIFI_EVENT_AP_ENABLING:
		ui_module_set_led(LED_WIFI, LED_STATE_BLINK_FAST);
		break;
	case WIFI_EVENT_AP_ENABLED:
		ui_module_set_led(LED_WIFI, LED_STATE_ON);
		break;
	case WIFI_EVENT_CLIENT_CONNECTED:
		ui_module_set_led(LED_WIFI, LED_STATE_PULSE);
		break;
	case WIFI_EVENT_AP_ERROR:
		ui_module_set_led(LED_WIFI, LED_STATE_BLINK_SLOW);
		break;
	default:
		break;
	}
}

static void bridge_event_observer(const struct zbus_channel *chan)
{
	const struct bridge_event *evt = zbus_chan_const_msg(chan);
	
	switch (evt->type) {
	case BRIDGE_EVENT_DISABLED:
		ui_module_set_led(LED_BRIDGE, LED_STATE_OFF);
		break;
	case BRIDGE_EVENT_ENABLING:
		ui_module_set_led(LED_BRIDGE, LED_STATE_BLINK_FAST);
		break;
	case BRIDGE_EVENT_ENABLED:
		ui_module_set_led(LED_BRIDGE, LED_STATE_ON);
		break;
	case BRIDGE_EVENT_PACKET_FORWARDED:
		/* Brief pulse on packet forward */
		ui_module_set_led(LED_BRIDGE, LED_STATE_PULSE);
		break;
	case BRIDGE_EVENT_ERROR:
		ui_module_set_led(LED_BRIDGE, LED_STATE_BLINK_SLOW);
		break;
	default:
		break;
	}
}

static void power_event_observer(const struct zbus_channel *chan)
{
	const struct power_event *evt = zbus_chan_const_msg(chan);
	
	switch (evt->type) {
	case POWER_EVENT_BATTERY_LOW:
		ui_module_set_led(LED_POWER, LED_STATE_BLINK_SLOW);
		break;
	case POWER_EVENT_BATTERY_CRITICAL:
		ui_module_set_led(LED_POWER, LED_STATE_BLINK_FAST);
		break;
	case POWER_EVENT_CHARGING_STARTED:
		ui_module_set_led(LED_POWER, LED_STATE_PULSE);
		break;
	case POWER_EVENT_CHARGING_STOPPED:
		ui_module_set_led(LED_POWER, LED_STATE_ON);
		break;
	case POWER_EVENT_POWER_SAVE_ENTERED:
		/* Turn off all LEDs in power save */
		for (int i = 0; i < 4; i++) {
			ui_module_set_led(i, LED_STATE_OFF);
		}
		break;
	default:
		break;
	}
}

ZBUS_LISTENER_DEFINE(ui_cellular_listener, cellular_event_observer);
ZBUS_LISTENER_DEFINE(ui_wifi_listener, wifi_event_observer);
ZBUS_LISTENER_DEFINE(ui_bridge_listener, bridge_event_observer);
ZBUS_LISTENER_DEFINE(ui_power_listener, power_event_observer);

#endif /* CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED */

/* Public API implementations */
int ui_module_init(void)
{
#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED
	int ret;
	
	LOG_INF("Initializing UI module");
	
	/* Initialize DK library */
	ret = dk_leds_init();
	if (ret) {
		LOG_ERR("Failed to initialize LEDs: %d", ret);
		return ret;
	}
	
	ret = dk_buttons_init(button_pressed);
	if (ret) {
		LOG_ERR("Failed to initialize buttons: %d", ret);
		return ret;
	}
	
	/* Initialize work items */
	for (int i = 0; i < 4; i++) {
		k_work_init_delayable(&ui_ctx.led_work[i], led_work_handler);
		ui_ctx.led_states[i] = LED_STATE_OFF;
	}
	
	k_work_init(&ui_ctx.button_work, button_work_handler);
	
	/* Initialize timers */
	for (int i = 0; i < 2; i++) {
		k_timer_init(&ui_ctx.long_press_timer[i], long_press_timer_handler, NULL);
	}
	
	ui_ctx.enabled = true;
	
	/* Initial LED test */
	for (int i = 0; i < 4; i++) {
		dk_set_led(i, 1);
		k_msleep(100);
		dk_set_led(i, 0);
	}
	
	LOG_INF("UI module initialized");
	return 0;
#else
	LOG_INF("UI module disabled by configuration");
	return 0;
#endif
}

int ui_module_set_led(enum led_id led_id, enum led_state state)
{
#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED
	if (!ui_ctx.enabled || led_id >= 4) {
		return -EINVAL;
	}

	/* Cancel any ongoing LED work */
	k_work_cancel_delayable(&ui_ctx.led_work[led_id]);

	ui_ctx.led_states[led_id] = state;

	switch (state) {
	case LED_STATE_OFF:
		dk_set_led(led_id, 0);
		break;

	case LED_STATE_ON:
		dk_set_led(led_id, 1);
		break;

	case LED_STATE_BLINK_SLOW:
	case LED_STATE_BLINK_FAST:
	case LED_STATE_PULSE:
		/* Start the LED work */
		k_work_schedule(&ui_ctx.led_work[led_id], K_NO_WAIT);
		break;

	default:
		return -EINVAL;
	}

	struct ui_event evt = {
		.type = UI_EVENT_LED_STATE_CHANGED,
		.led_id = led_id,
		.led_state = (state != LED_STATE_OFF)
	};
	zbus_chan_pub(&ui_chan, &evt, K_MSEC(100));

	return 0;
#else
	return 0;
#endif
}

bool ui_module_get_button(enum button_id button_id)
{
#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED
	if (!ui_ctx.enabled || button_id >= 2) {
		return false;
	}

	return ui_ctx.button_pressed[button_id];
#else
	return false;
#endif
}

int ui_module_enable(bool enable)
{
#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED
	ui_ctx.enabled = enable;

	if (!enable) {
		/* Turn off all LEDs and cancel work */
		for (int i = 0; i < 4; i++) {
			k_work_cancel_delayable(&ui_ctx.led_work[i]);
			dk_set_led(i, 0);
		}
		LOG_INF("UI disabled for power saving");
	} else {
		LOG_INF("UI enabled");
	}

	return 0;
#else
	return 0;
#endif
}

bool ui_module_is_enabled(void)
{
#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED
	return ui_ctx.enabled;
#else
	return false;
#endif
}
