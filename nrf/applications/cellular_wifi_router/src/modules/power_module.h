/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef POWER_MODULE_H_
#define POWER_MODULE_H_

#include <zephyr/kernel.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize the power module
 * 
 * @return 0 on success, negative error code on failure
 */
int power_module_init(void);

/**
 * @brief Get battery level percentage
 * 
 * @return Battery level (0-100) or negative error code
 */
int power_module_get_battery_level(void);

/**
 * @brief Check if device is charging
 * 
 * @return true if charging, false otherwise
 */
bool power_module_is_charging(void);

/**
 * @brief Enter power save mode
 * 
 * @return 0 on success, negative error code on failure
 */
int power_module_enter_power_save(void);

/**
 * @brief Exit power save mode
 * 
 * @return 0 on success, negative error code on failure
 */
int power_module_exit_power_save(void);

/**
 * @brief Check if in power save mode
 * 
 * @return true if in power save mode, false otherwise
 */
bool power_module_is_power_save_active(void);

#ifdef __cplusplus
}
#endif

#endif /* POWER_MODULE_H_ */
