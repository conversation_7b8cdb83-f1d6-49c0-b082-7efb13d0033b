/*
 * Cellular WiFi Router - Power Module Header
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef POWER_MODULE_H
#define POWER_MODULE_H

#include <zephyr/kernel.h>
#include <zephyr/pm/pm.h>
#include <zephyr/pm/device.h>
#include "../common/events.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Power Module States */
enum power_state {
	POWER_STATE_DISABLED,
	POWER_STATE_INITIALIZING,
	POWER_STATE_NORMAL,
	POWER_STATE_LOW_POWER,
	POWER_STATE_CRITICAL,
	POWER_STATE_CHARGING,
	POWER_STATE_ERROR,
};

/* Power Management Modes */
enum power_mode {
	POWER_MODE_PERFORMANCE,
	POWER_MODE_BALANCED,
	POWER_MODE_POWER_SAVE,
	POWER_MODE_ULTRA_LOW_POWER,
};

/* Battery Information */
struct battery_info {
	uint16_t voltage_mv;
	uint8_t percentage;
	int16_t current_ma;
	int16_t temperature_c;
	bool charging;
	bool usb_connected;
	bool low_battery;
	bool critical_battery;
	uint32_t time_to_empty_min;
	uint32_t time_to_full_min;
};

/* Power Configuration */
struct power_config {
	uint16_t low_battery_threshold_mv;
	uint16_t critical_battery_threshold_mv;
	uint32_t cellular_idle_timeout_sec;
	uint32_t wifi_idle_timeout_sec;
	uint32_t system_idle_timeout_sec;
	bool auto_power_save;
	bool ui_disable_on_low_power;
	enum power_mode default_mode;
	uint8_t led_brightness_normal;
	uint8_t led_brightness_low_power;
};

/* Power Statistics */
struct power_stats {
	enum power_state current_state;
	enum power_mode current_mode;
	uint32_t uptime_sec;
	uint32_t time_in_low_power_sec;
	uint32_t time_charging_sec;
	uint32_t power_cycles;
	uint32_t low_battery_events;
	uint32_t critical_battery_events;
	uint16_t min_voltage_mv;
	uint16_t max_voltage_mv;
	uint16_t avg_voltage_mv;
};

/* Power Module Functions */
int power_module_init(void);
int power_module_shutdown(void);
bool power_module_is_healthy(void);

/* Power State Management */
int power_set_mode(enum power_mode mode);
enum power_mode power_get_mode(void);
enum power_state power_get_state(void);

/* Battery Monitoring */
int power_get_battery_info(struct battery_info *info);
int power_get_battery_voltage(uint16_t *voltage_mv);
int power_get_battery_percentage(uint8_t *percentage);
bool power_is_charging(void);
bool power_is_usb_connected(void);
bool power_is_low_battery(void);
bool power_is_critical_battery(void);

/* Configuration Management */
int power_set_config(const struct power_config *config);
int power_get_config(struct power_config *config);
int power_load_config(void);
int power_save_config(void);

/* Statistics and Monitoring */
int power_get_stats(struct power_stats *stats);
int power_reset_stats(void);

/* Power Management Control */
int power_enter_low_power_mode(void);
int power_exit_low_power_mode(void);
int power_enter_deep_sleep(uint32_t duration_sec);
int power_system_shutdown(void);
int power_system_reboot(void);

/* Device Power Management */
int power_enable_device(const struct device *dev, bool enable);
int power_set_device_state(const struct device *dev, enum pm_device_state state);
int power_get_device_state(const struct device *dev, enum pm_device_state *state);

/* Module Power Control */
int power_cellular_enable(bool enable);
int power_wifi_enable(bool enable);
int power_ui_enable(bool enable);
int power_sensors_enable(bool enable);

/* Charging Management */
int power_set_charging_current(uint16_t current_ma);
int power_get_charging_current(uint16_t *current_ma);
int power_enable_charging(bool enable);
bool power_is_charging_enabled(void);

/* Power Optimization */
int power_optimize_for_battery_life(void);
int power_optimize_for_performance(void);
int power_set_cpu_frequency(uint32_t freq_hz);
int power_get_cpu_frequency(uint32_t *freq_hz);

/* Voltage Regulation */
int power_set_regulator_voltage(const char *regulator_name, uint32_t voltage_uv);
int power_get_regulator_voltage(const char *regulator_name, uint32_t *voltage_uv);
int power_enable_regulator(const char *regulator_name, bool enable);

/* Power Events and Callbacks */
typedef void (*power_event_callback_t)(enum power_event_type type, void *data);
int power_register_event_callback(power_event_callback_t callback);

/* Watchdog and Safety */
int power_enable_watchdog(uint32_t timeout_sec);
int power_disable_watchdog(void);
int power_feed_watchdog(void);

/* Temperature Monitoring */
int power_get_temperature(int16_t *temperature_c);
int power_set_temperature_limits(int16_t low_c, int16_t high_c);

/* Power Profiling */
int power_start_profiling(void);
int power_stop_profiling(void);
int power_get_power_consumption(uint32_t *power_mw);

/* Emergency Functions */
int power_emergency_shutdown(void);
int power_force_reboot(void);

#ifdef __cplusplus
}
#endif

#endif /* POWER_MODULE_H */
