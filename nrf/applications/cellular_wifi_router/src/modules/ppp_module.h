/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef PPP_MODULE_H_
#define PPP_MODULE_H_

/** @file ppp_module.h
 *
 * @brief PPP module for cellular WiFi router
 * @{
 */

#include <zephyr/types.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* PPP module events */
enum ppp_module_event_type {
	PPP_MODULE_EVENT_STARTED,
	PPP_MODULE_EVENT_STOPPED,
	PPP_MODULE_EVENT_CONNECTED,
	PPP_MODULE_EVENT_DISCONNECTED,
	PPP_MODULE_EVENT_ERROR
};

/* PPP module state */
enum ppp_module_state {
	PPP_MODULE_STATE_STOPPED,
	PPP_MODULE_STATE_STARTING,
	PPP_MODULE_STATE_RUNNING,
	PPP_MODULE_STATE_STOPPING,
	PPP_MODULE_STATE_ERROR
};

/* PPP configuration */
struct ppp_module_config {
	bool auto_start;
	uint32_t mtu;
	uint32_t mru;
	bool ipcp_enabled;
	bool ipv6cp_enabled;
	char local_ip[16];
	char remote_ip[16];
	char dns_primary[16];
	char dns_secondary[16];
};

/**
 * @brief Initialize PPP module
 *
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_init(void);

/**
 * @brief Shutdown PPP module
 *
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_shutdown(void);

/**
 * @brief Check if PPP module is healthy
 *
 * @retval true if healthy, false otherwise
 */
bool ppp_module_is_healthy(void);

/**
 * @brief Start PPP service
 *
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_start(void);

/**
 * @brief Stop PPP service
 *
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_stop(void);

/**
 * @brief Check if PPP is active
 *
 * @retval true if PPP is active, false otherwise
 */
bool ppp_module_is_active(void);

/**
 * @brief Check if PPP is connected
 *
 * @retval true if PPP is connected, false otherwise
 */
bool ppp_module_is_connected(void);

/**
 * @brief Get PPP module state
 *
 * @retval Current PPP module state
 */
enum ppp_module_state ppp_module_get_state(void);

/**
 * @brief Get PPP configuration
 *
 * @param config Pointer to configuration structure to fill
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_get_config(struct ppp_module_config *config);

/**
 * @brief Set PPP configuration
 *
 * @param config Pointer to configuration structure
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_set_config(const struct ppp_module_config *config);

/**
 * @brief Get PPP statistics
 *
 * @param tx_bytes Pointer to store transmitted bytes count
 * @param rx_bytes Pointer to store received bytes count
 * @param tx_packets Pointer to store transmitted packets count
 * @param rx_packets Pointer to store received packets count
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_get_stats(uint64_t *tx_bytes, uint64_t *rx_bytes,
			 uint64_t *tx_packets, uint64_t *rx_packets);

/**
 * @brief Reset PPP statistics
 *
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_reset_stats(void);

/**
 * @brief Handle PPP data from UART
 *
 * @param data Pointer to data buffer
 * @param len Length of data
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_handle_uart_data(const uint8_t *data, size_t len);

/**
 * @brief Send PPP data to UART
 *
 * @param data Pointer to data buffer
 * @param len Length of data
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int ppp_module_send_uart_data(const uint8_t *data, size_t len);

/**
 * @brief Enable PPP forwarding of CGEV notifications
 *
 * @param enable True to enable, false to disable
 */
void ppp_module_set_cgev_forwarding(bool enable);

/**
 * @brief Check if PPP forwarding of CGEV notifications is enabled
 *
 * @retval true if enabled, false otherwise
 */
bool ppp_module_get_cgev_forwarding(void);

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* PPP_MODULE_H_ */
