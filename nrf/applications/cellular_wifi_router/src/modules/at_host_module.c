/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include "at_host_module.h"
#include "../common/events.h"
#include "../common/utils.h"
#include <zephyr/logging/log.h>
#include <zephyr/kernel.h>
#include <zephyr/drivers/uart.h>
#include <zephyr/sys/ring_buffer.h>
#include <zephyr/pm/device.h>
#include <stdio.h>
#include <string.h>
#include <stdarg.h>

LOG_MODULE_REGISTER(at_host_module, CONFIG_LOG_DEFAULT_LEVEL);

/* AT command strings */
#define AT_HOST_SYNC_STR    "Ready\r\n"
#define AT_HOST_OK_STR      "\r\nOK\r\n"
#define AT_HOST_ERROR_STR   "\r\nERROR\r\n"
#define AT_HOST_CRLF_STR    "\r\n"
#define AT_HOST_CR          '\r'
#define AT_HOST_LF          '\n'

/* Operation mode variables */
enum at_host_operation_mode {
	AT_HOST_COMMAND_MODE,   /* AT command host */
	AT_HOST_DATA_MODE,      /* Raw data sending */
	AT_HOST_NULL_MODE       /* Discard incoming until next command */
};

/* Module state */
static struct at_host_backend at_backend;
static enum at_host_operation_mode at_mode;
static at_host_datamode_handler_t datamode_handler;
static int datamode_handler_result;
static bool module_initialized = false;

/* Buffers */
uint8_t at_host_cmd_buf[AT_HOST_MAX_CMD_LEN + 1];
uint8_t at_host_data_buf[AT_HOST_MAX_MESSAGE_SIZE];
uint16_t at_host_datamode_time_limit;

/* Ring buffer for data mode - disabled for now */
/* RING_BUF_DECLARE(data_rb, 2048); */
/* static uint8_t quit_str_partial_match; */

/* Mutexes */
K_MUTEX_DEFINE(mutex_mode);
K_MUTEX_DEFINE(mutex_data);

/* Work items */
static struct k_work raw_send_scheduled_work;

/* UART device */
static const struct device *uart_dev;

/* Forward declarations */
static void at_host_uart_callback(const struct device *dev, struct uart_event *evt, void *user_data);
static void raw_send_work_fn(struct k_work *work);
static int at_host_uart_init(void);
static void at_host_process_command(const char *cmd);

/* Get current operation mode */
static enum at_host_operation_mode get_at_mode(void)
{
	enum at_host_operation_mode mode;
	
	k_mutex_lock(&mutex_mode, K_FOREVER);
	mode = at_mode;
	k_mutex_unlock(&mutex_mode);
	
	return mode;
}

/* Set operation mode (mutex must be locked) */
static bool set_at_mode(enum at_host_operation_mode mode)
{
	bool ret = false;
	
	if (at_mode == AT_HOST_COMMAND_MODE) {
		if (mode == AT_HOST_DATA_MODE) {
			ret = true;
		}
	} else if (at_mode == AT_HOST_DATA_MODE) {
		if (mode == AT_HOST_NULL_MODE || mode == AT_HOST_COMMAND_MODE) {
			ret = true;
		}
	} else if (at_mode == AT_HOST_NULL_MODE) {
		if (mode == AT_HOST_COMMAND_MODE || mode == AT_HOST_NULL_MODE) {
			ret = true;
		}
	}
	
	if (ret) {
		LOG_DBG("AT mode changed: %d -> %d", at_mode, mode);
		at_mode = mode;
	} else {
		LOG_ERR("Failed to change AT mode: %d -> %d", at_mode, mode);
	}
	
	return ret;
}

/* Exit data mode */
static bool exit_datamode(void)
{
	bool ret = false;
	
	k_mutex_lock(&mutex_mode, K_FOREVER);
	if (at_mode == AT_HOST_DATA_MODE) {
		ret = set_at_mode(AT_HOST_COMMAND_MODE);
		if (ret) {
			datamode_handler = NULL;
		}
	}
	k_mutex_unlock(&mutex_mode);
	
	return ret;
}

/* UART initialization */
static int at_host_uart_init(void)
{
	int err;
	
	uart_dev = DEVICE_DT_GET(DT_CHOSEN(zephyr_console));
	if (!device_is_ready(uart_dev)) {
		LOG_ERR("UART device not ready");
		return -ENODEV;
	}
	
	err = uart_callback_set(uart_dev, at_host_uart_callback, NULL);
	if (err) {
		LOG_ERR("Failed to set UART callback: %d", err);
		return err;
	}
	
	/* Enable UART RX */
	err = uart_rx_enable(uart_dev, at_host_cmd_buf, sizeof(at_host_cmd_buf), 100);
	if (err) {
		LOG_ERR("Failed to enable UART RX: %d", err);
		return err;
	}
	
	return 0;
}

/* UART callback */
static void at_host_uart_callback(const struct device *dev, struct uart_event *evt, void *user_data)
{
	ARG_UNUSED(dev);
	ARG_UNUSED(user_data);
	
	switch (evt->type) {
	case UART_RX_RDY:
		at_host_receive(evt->data.rx.buf + evt->data.rx.offset, evt->data.rx.len);
		break;
		
	case UART_RX_BUF_REQUEST:
		uart_rx_buf_rsp(uart_dev, at_host_cmd_buf, sizeof(at_host_cmd_buf));
		break;
		
	case UART_RX_BUF_RELEASED:
		/* Buffer released, nothing to do */
		break;
		
	case UART_RX_DISABLED:
		LOG_WRN("UART RX disabled");
		break;
		
	case UART_TX_DONE:
		/* TX complete, nothing to do */
		break;
		
	case UART_TX_ABORTED:
		LOG_WRN("UART TX aborted");
		break;
		
	default:
		LOG_WRN("Unknown UART event: %d", evt->type);
		break;
	}
}

/* Process AT command */
static void at_host_process_command(const char *cmd)
{
	char response[AT_HOST_MAX_RESPONSE_LEN];
	int ret;
	
	LOG_DBG("Processing AT command: %s", cmd);
	
	/* Handle basic AT commands */
	if (strcmp(cmd, "AT") == 0) {
		at_host_rsp_send_ok();
		return;
	}
	
	if (strcmp(cmd, "ATI") == 0) {
		at_host_rsp_send("Cellular WiFi Router\r\n");
		at_host_rsp_send_ok();
		return;
	}
	
	/* Forward to modem for MFW commands */
	ret = nrf_modem_at_cmd(response, sizeof(response), "%s", cmd);
	if (ret == 0) {
		/* Success - send response */
		if (strlen(response) > 0) {
			at_host_send_str(response);
		}
	} else {
		/* Error - send error response */
		at_host_rsp_send_error();
	}
}

/* Work function for raw data sending */
static void raw_send_work_fn(struct k_work *work)
{
	ARG_UNUSED(work);
	/* TODO: Implement raw data sending */
}

/* Module initialization */
int at_host_module_init(void)
{
	int ret;
	
	if (module_initialized) {
		LOG_WRN("AT host module already initialized");
		return 0;
	}
	
	LOG_INF("Initializing AT host module");
	
	/* Initialize work items */
	k_work_init(&raw_send_scheduled_work, raw_send_work_fn);
	
	/* Initialize UART */
	ret = at_host_uart_init();
	if (ret) {
		LOG_ERR("Failed to initialize UART: %d", ret);
		return ret;
	}
	
	/* Set initial mode */
	at_mode = AT_HOST_COMMAND_MODE;
	datamode_handler = NULL;
	datamode_handler_result = 0;
	
	/* Send ready message */
	at_host_send_str(AT_HOST_SYNC_STR);
	
	module_initialized = true;
	LOG_INF("AT host module initialized successfully");
	
	return 0;
}

/* Module shutdown */
int at_host_module_shutdown(void)
{
	if (!module_initialized) {
		return 0;
	}
	
	LOG_INF("Shutting down AT host module");
	
	/* Disable UART */
	if (uart_dev) {
		uart_rx_disable(uart_dev);
	}
	
	module_initialized = false;
	LOG_INF("AT host module shutdown completed");
	
	return 0;
}

/* Check if module is healthy */
bool at_host_module_is_healthy(void)
{
	return module_initialized && uart_dev && device_is_ready(uart_dev);
}

/* Set AT backend */
int at_host_set_backend(struct at_host_backend backend)
{
	at_backend = backend;
	return at_backend.start ? at_backend.start() : 0;
}

/* Send data */
int at_host_send(const uint8_t *data, size_t len)
{
	if (at_backend.send) {
		return at_backend.send(data, len);
	}
	
	/* Default UART send */
	if (!uart_dev) {
		return -ENODEV;
	}
	
	return uart_tx(uart_dev, data, len, SYS_FOREVER_US);
}

/* Send string */
int at_host_send_str(const char *str)
{
	return at_host_send((const uint8_t *)str, strlen(str));
}

/* Process received data */
void at_host_receive(const uint8_t *data, size_t len)
{
	static char cmd_buffer[AT_HOST_MAX_CMD_LEN + 1];
	static size_t cmd_pos = 0;
	
	enum at_host_operation_mode mode = get_at_mode();
	
	if (mode == AT_HOST_DATA_MODE) {
		/* Handle data mode - simplified for now */
		/* TODO: Implement proper data mode handling */
		LOG_DBG("Data mode handling not implemented yet");
		return;
	}
	
	if (mode == AT_HOST_NULL_MODE) {
		/* Discard data */
		return;
	}
	
	/* Command mode - process AT commands */
	for (size_t i = 0; i < len; i++) {
		char c = data[i];
		
		if (c == AT_HOST_CR || c == AT_HOST_LF) {
			if (cmd_pos > 0) {
				cmd_buffer[cmd_pos] = '\0';
				at_host_process_command(cmd_buffer);
				cmd_pos = 0;
			}
		} else if (cmd_pos < AT_HOST_MAX_CMD_LEN) {
			cmd_buffer[cmd_pos++] = c;
		} else {
			/* Command too long - reset */
			cmd_pos = 0;
			at_host_rsp_send_error();
		}
	}
}

/* Power management functions */
int at_host_power_off(void)
{
	if (at_backend.stop) {
		return at_backend.stop();
	}
	return 0;
}

int at_host_power_on(void)
{
	if (at_backend.start) {
		return at_backend.start();
	}
	return 0;
}

/* Response functions */
void at_host_rsp_send(const char *fmt, ...)
{
	char buffer[AT_HOST_MAX_RESPONSE_LEN];
	va_list args;
	
	va_start(args, fmt);
	vsnprintf(buffer, sizeof(buffer), fmt, args);
	va_end(args);
	
	at_host_send_str(buffer);
}

void at_host_rsp_send_ok(void)
{
	at_host_send_str(AT_HOST_OK_STR);
}

void at_host_rsp_send_error(void)
{
	at_host_send_str(AT_HOST_ERROR_STR);
}

void at_host_data_send(const uint8_t *data, size_t len)
{
	at_host_send(data, len);
}

/* Data mode functions */
int at_host_enter_datamode(at_host_datamode_handler_t handler)
{
	bool ret = false;
	
	k_mutex_lock(&mutex_mode, K_FOREVER);
	if (at_mode == AT_HOST_COMMAND_MODE) {
		ret = set_at_mode(AT_HOST_DATA_MODE);
		if (ret) {
			datamode_handler = handler;
		}
	}
	k_mutex_unlock(&mutex_mode);
	
	return ret ? 0 : -EBUSY;
}

bool at_host_in_datamode(void)
{
	return get_at_mode() == AT_HOST_DATA_MODE;
}

bool at_host_exit_datamode_handler(int result)
{
	datamode_handler_result = result;
	return exit_datamode();
}

/* AT command callback wrapper */
int at_host_cb_wrapper(char *buf, size_t len, char *at_cmd, at_host_callback_t cb)
{
	struct at_parser parser;
	enum at_parser_cmd_type cmd_type;
	uint32_t param_count;
	int ret;
	
	ret = at_parser_init(&parser, at_cmd);
	if (ret) {
		return ret;
	}
	
	ret = at_parser_cmd_type_get(&parser, &cmd_type);
	if (ret) {
		return ret;
	}
	
	ret = at_parser_cmd_count_get(&parser, &param_count);
	if (ret) {
		return ret;
	}
	
	return cb(cmd_type, &parser, param_count);
}
