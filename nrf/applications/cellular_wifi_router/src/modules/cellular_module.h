/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef CELLULAR_MODULE_H_
#define CELLULAR_MODULE_H_

#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize the cellular module
 * 
 * @return 0 on success, negative error code on failure
 */
int cellular_module_init(void);

/**
 * @brief Start the cellular module
 * 
 * @return 0 on success, negative error code on failure
 */
int cellular_module_start(void);

/**
 * @brief Stop the cellular module
 * 
 * @return 0 on success, negative error code on failure
 */
int cellular_module_stop(void);

/**
 * @brief Get cellular network interface
 * 
 * @return Pointer to network interface or NULL if not available
 */
struct net_if *cellular_module_get_iface(void);

/**
 * @brief Get cellular connection status
 * 
 * @return true if connected, false otherwise
 */
bool cellular_module_is_connected(void);

/**
 * @brief Get signal strength
 * 
 * @return Signal strength in dBm or INT_MIN if not available
 */
int cellular_module_get_signal_strength(void);

/**
 * @brief Get operator name
 * 
 * @param buf Buffer to store operator name
 * @param buf_size Size of the buffer
 * @return 0 on success, negative error code on failure
 */
int cellular_module_get_operator(char *buf, size_t buf_size);

#ifdef __cplusplus
}
#endif

#endif /* CELLULAR_MODULE_H_ */
