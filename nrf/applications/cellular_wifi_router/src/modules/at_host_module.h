/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef AT_HOST_MODULE_H_
#define AT_HOST_MODULE_H_

/** @file at_host_module.h
 *
 * @brief AT host module for cellular WiFi router
 * @{
 */

#include <zephyr/types.h>
#include <ctype.h>
#include <nrf_modem_at.h>
#include <modem/at_monitor.h>
#include <modem/at_cmd_custom.h>
#include <modem/at_parser.h>

#ifdef __cplusplus
extern "C" {
#endif

/* AT command buffer sizes */
#define AT_HOST_MAX_CMD_LEN     4096
#define AT_HOST_MAX_RESPONSE_LEN 2048
#define AT_HOST_MAX_MESSAGE_SIZE 1500

/* AT response delay for low baud rates */
#define AT_HOST_UART_RESPONSE_DELAY K_MSEC(50)

/* Data mode flags */
#define AT_HOST_DATAMODE_FLAGS_NONE         0
#define AT_HOST_DATAMODE_FLAGS_MORE_DATA    (1 << 0)
#define AT_HOST_DATAMODE_FLAGS_EXIT_HANDLER (1 << 1)

/* External buffers */
extern uint8_t at_host_data_buf[AT_HOST_MAX_MESSAGE_SIZE];
extern uint8_t at_host_cmd_buf[AT_HOST_MAX_CMD_LEN + 1];

/* Data mode time limit */
extern uint16_t at_host_datamode_time_limit;

/** @brief Operations in data mode. */
enum at_host_datamode_operation {
	AT_HOST_DATAMODE_SEND,  /* Send data in datamode */
	AT_HOST_DATAMODE_EXIT   /* Exit data mode */
};

/** @brief Data mode sending handler type.
 *
 * @retval 0 means all data is sent successfully.
 *         Positive value means the actual size of bytes that has been sent.
 *         Negative value means error occurs in sending.
 */
typedef int (*at_host_datamode_handler_t)(uint8_t op, const uint8_t *data, int len, uint8_t flags);

/* AT backend interface */
struct at_host_backend {
	int (*start)(void);
	int (*send)(const uint8_t *data, size_t len);
	int (*stop)(void);
};

/** @brief AT command callback type. */
typedef int at_host_callback_t(enum at_parser_cmd_type cmd_type, struct at_parser *parser,
			       uint32_t param_count);

/**
 * @brief Initialize AT host module
 *
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int at_host_module_init(void);

/**
 * @brief Shutdown AT host module
 *
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int at_host_module_shutdown(void);

/**
 * @brief Check if AT host module is healthy
 *
 * @retval true if healthy, false otherwise
 */
bool at_host_module_is_healthy(void);

/**
 * @brief Set AT backend
 *
 * @param backend Backend interface
 * @retval 0 on success (the new backend is successfully started).
 */
int at_host_set_backend(struct at_host_backend backend);

/**
 * @brief Send data via current AT backend
 *
 * @param data Data to send
 * @param len Length of data
 * @retval 0 on success.
 */
int at_host_send(const uint8_t *data, size_t len);

/**
 * @brief Send string via current AT backend
 *
 * @param str String to send
 * @retval 0 on success.
 */
int at_host_send_str(const char *str);

/**
 * @brief Process received AT bytes
 *
 * @param data Received data
 * @param len Length of received data
 */
void at_host_receive(const uint8_t *data, size_t len);

/**
 * @brief Turn AT backend power off
 *
 * @retval 0 on success, or a (negative) error code.
 */
int at_host_power_off(void);

/**
 * @brief Turn AT backend power on
 *
 * @retval 0 on success, or a (negative) error code.
 */
int at_host_power_on(void);

/**
 * @brief Send AT command response
 *
 * @param fmt Response message format string
 */
void at_host_rsp_send(const char *fmt, ...);

/**
 * @brief Send AT command response of OK
 */
void at_host_rsp_send_ok(void);

/**
 * @brief Send AT command response of ERROR
 */
void at_host_rsp_send_error(void);

/**
 * @brief Send raw data received in data mode
 *
 * @param data Raw data received
 * @param len Length of raw data
 */
void at_host_data_send(const uint8_t *data, size_t len);

/**
 * @brief Request AT host to enter data mode
 *
 * @param handler Data mode handler provided by requesting module
 * @retval 0 If the operation was successful.
 *         Otherwise, a (negative) error code is returned.
 */
int at_host_enter_datamode(at_host_datamode_handler_t handler);

/**
 * @brief Check whether AT host is in data mode
 *
 * @retval true if yes, false if no.
 */
bool at_host_in_datamode(void);

/**
 * @brief Exit the data mode handler
 *
 * @param result Result of sending in data mode.
 * @retval true If handler has closed successfully.
 *         false If not in data mode.
 */
bool at_host_exit_datamode_handler(int result);

/**
 * @brief Generic wrapper for a custom AT command callback.
 *
 * @param buf Response buffer.
 * @param len Response buffer size.
 * @param at_cmd AT command.
 * @param cb AT command callback.
 * @retval 0 on success.
 */
int at_host_cb_wrapper(char *buf, size_t len, char *at_cmd, at_host_callback_t cb);

/**
 * @brief Define a wrapper for a custom AT command callback.
 *
 * @param entry The entry name.
 * @param _filter The (partial) AT command on which the callback should trigger.
 * @param _callback The AT command handler callback.
 */
#define AT_HOST_CMD_CUSTOM(entry, _filter, _callback)                                          \
	static int _callback(enum at_parser_cmd_type cmd_type, struct at_parser *parser,       \
			     uint32_t);                                                        \
	static int _callback##_wrapper_##entry(char *buf, size_t len, char *at_cmd)            \
	{                                                                                      \
		return at_host_cb_wrapper(buf, len, at_cmd, _callback);                       \
	}                                                                                      \
	AT_CMD_CUSTOM(entry, _filter, _callback##_wrapper_##entry);

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* AT_HOST_MODULE_H_ */
