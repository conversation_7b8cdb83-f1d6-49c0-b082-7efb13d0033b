/*
 * Cellular WiFi Router - Cellular Module Implementation
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include "cellular_module.h"
#include "../common/events.h"
#include "../common/utils.h"
#include <zephyr/logging/log.h>
#include <zephyr/zbus/zbus.h>
#include <zephyr/smf.h>
#include <zephyr/net/net_mgmt.h>
#include <zephyr/net/net_event.h>
#include <modem/lte_lc.h>
#include <modem/pdn.h>
#include <modem/modem_info.h>
#include <nrf_modem_at.h>

LOG_MODULE_REGISTER(cellular_module, CONFIG_LOG_DEFAULT_LEVEL);

/* State Machine Context */
static struct smf_ctx cellular_smf_ctx;
static enum cellular_state current_state = CELLULAR_STATE_DISABLED;
static bool cellular_healthy = false;

/* Module Configuration */
static struct cellular_config config = {
	.apn = "",
	.family = PDN_FAM_IPV4,
	.auto_connect = true,
	.connect_timeout_sec = 120,
	.idle_timeout_sec = 300,
	.power_saving_enable = true,
	.preferred_mode = LTE_LC_LTE_MODE_LTEM,
};

/* Module Statistics */
static struct cellular_stats stats = {0};

/* Network Interface */
static struct net_if *cellular_iface = NULL;
static int pdn_id = 0;

/* Work items */
static struct k_work_delayable connect_work;
static struct k_work_delayable stats_work;
static struct k_work_delayable idle_timeout_work;

/* Event callback */
static cellular_event_callback_t event_callback = NULL;

/* ZBUS Observers */
ZBUS_SUBSCRIBER_DEFINE(cellular_power_events_sub, 4);

/* Forward declarations */
static void lte_handler(const struct lte_lc_evt *const evt);
static void pdn_event_handler(uint8_t cid, enum pdn_event event, int reason);
static void connect_work_handler(struct k_work *work);
static void stats_work_handler(struct k_work *work);
static void idle_timeout_work_handler(struct k_work *work);
static int cellular_configure_modem(void);
static int cellular_start_connection(void);
static void cellular_publish_event(enum cellular_event_type type, int error_code);

/* LTE Link Controller event handler */
static void lte_handler(const struct lte_lc_evt *const evt)
{
	switch (evt->type) {
	case LTE_LC_EVT_NW_REG_STATUS:
		LOG_INF("Network registration status: %d", evt->nw_reg_status);
		stats.reg_status = evt->nw_reg_status;
		
		switch (evt->nw_reg_status) {
		case LTE_LC_NW_REG_REGISTERED_HOME:
		case LTE_LC_NW_REG_REGISTERED_ROAMING:
			LOG_INF("Connected to cellular network");
			current_state = CELLULAR_STATE_CONNECTED;
			cellular_publish_event(CELLULAR_EVENT_CONNECTED, 0);
			break;
			
		case LTE_LC_NW_REG_SEARCHING:
			LOG_INF("Searching for cellular network");
			current_state = CELLULAR_STATE_SEARCHING;
			cellular_publish_event(CELLULAR_EVENT_CONNECTING, 0);
			break;
			
		case LTE_LC_NW_REG_NOT_REGISTERED:
		case LTE_LC_NW_REG_REGISTRATION_DENIED:
			LOG_WRN("Network registration failed");
			current_state = CELLULAR_STATE_DISCONNECTED;
			cellular_publish_event(CELLULAR_EVENT_DISCONNECTED, -ENETUNREACH);
			break;
			
		default:
			break;
		}
		break;
		
	/* PSM update event removed in newer API */
		
	case LTE_LC_EVT_RRC_UPDATE:
		LOG_DBG("RRC mode: %s", evt->rrc_mode == LTE_LC_RRC_MODE_CONNECTED ?
			"Connected" : "Idle");
		break;
		
	case LTE_LC_EVT_CELL_UPDATE:
		LOG_INF("Cell update: ID %d, TAC %d", evt->cell.id, evt->cell.tac);
		stats.cell_id = evt->cell.id;
		stats.tracking_area_code = evt->cell.tac;
		break;
		
	case LTE_LC_EVT_LTE_MODE_UPDATE:
		LOG_INF("LTE mode update: %d", evt->lte_mode);
		stats.current_mode = evt->lte_mode;
		break;
		
	default:
		break;
	}
}

/* PDN event handler */
static void pdn_event_handler(uint8_t cid, enum pdn_event event, int reason)
{
	switch (event) {
	case PDN_EVENT_CNEC_ESM:
		LOG_INF("PDN event CNEC ESM: CID %d, reason %d", cid, reason);
		break;
		
	case PDN_EVENT_ACTIVATED:
		LOG_INF("PDN activated: CID %d", cid);
		if (cid == pdn_id) {
			cellular_publish_event(CELLULAR_EVENT_DATA_READY, 0);
		}
		break;
		
	case PDN_EVENT_DEACTIVATED:
		LOG_INF("PDN deactivated: CID %d", cid);
		if (cid == pdn_id) {
			current_state = CELLULAR_STATE_DISCONNECTED;
			cellular_publish_event(CELLULAR_EVENT_DISCONNECTED, reason);
		}
		break;
		
	case PDN_EVENT_IPV6_UP:
		LOG_INF("PDN IPv6 up: CID %d", cid);
		break;
		
	case PDN_EVENT_IPV6_DOWN:
		LOG_INF("PDN IPv6 down: CID %d", cid);
		break;
		
	default:
		LOG_WRN("Unknown PDN event: %d", event);
		break;
	}
}

/* Work handlers */
static void connect_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	LOG_INF("Starting cellular connection");
	
	int ret = cellular_start_connection();
	if (ret) {
		LOG_ERR("Failed to start cellular connection: %d", ret);
		current_state = CELLULAR_STATE_ERROR;
		cellular_publish_event(CELLULAR_EVENT_ERROR, ret);
	}
}

static void stats_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	/* Update signal strength - simplified for now */
	/* TODO: Implement proper signal strength reading */
	stats.rsrp = -80; /* Simulated value */
	stats.rsrq = -10; /* Simulated value */
	
	/* Update operator name */
	char operator_full_name[32];
	if (modem_info_string_get(MODEM_INFO_OPERATOR, operator_full_name, 
				  sizeof(operator_full_name)) == 0) {
		strncpy(stats.operator_name, operator_full_name, 
			sizeof(stats.operator_name) - 1);
	}
	
	/* Publish signal update event */
	cellular_publish_event(CELLULAR_EVENT_SIGNAL_UPDATE, 0);
	
	/* Reschedule stats work */
	k_work_reschedule(&stats_work, K_SECONDS(30));
}

static void idle_timeout_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	if (current_state == CELLULAR_STATE_CONNECTED && config.power_saving_enable) {
		LOG_INF("Cellular idle timeout - entering power saving mode");
		cellular_enter_power_saving();
	}
}

static int cellular_configure_modem(void)
{
	int ret;
	
	LOG_INF("Configuring cellular modem");
	
	/* Set LTE mode preference - simplified for now */
	/* TODO: Implement proper LTE mode setting */
	LOG_INF("LTE mode configuration skipped for now");
	
	/* Configure PSM if power saving is enabled */
	if (config.power_saving_enable) {
		ret = lte_lc_psm_req(true);
		if (ret) {
			LOG_WRN("Failed to request PSM: %d", ret);
			/* Continue anyway */
		}
	}
	
	/* Initialize PDN - simplified for now */
	/* TODO: Implement proper PDN initialization */
	LOG_INF("PDN initialization skipped for now");
	
	/* Configure APN if specified */
	if (strlen(config.apn) > 0) {
		ret = pdn_ctx_configure(pdn_id, config.apn, config.family, NULL);
		if (ret) {
			LOG_ERR("Failed to configure PDN context: %d", ret);
			return ret;
		}
	}
	
	LOG_INF("Cellular modem configured successfully");
	return 0;
}

static int cellular_start_connection(void)
{
	int ret;
	
	current_state = CELLULAR_STATE_CONNECTING;
	
	/* Connect to LTE network */
	ret = lte_lc_connect_async(lte_handler);
	if (ret) {
		LOG_ERR("Failed to start LTE connection: %d", ret);
		return ret;
	}
	
	LOG_INF("LTE connection started");
	return 0;
}

static void cellular_publish_event(enum cellular_event_type type, int error_code)
{
	struct cellular_event event = {
		.type = type,
		.reg_status = stats.reg_status,
		.lte_mode = stats.current_mode,
		.rsrp = stats.rsrp,
		.rsrq = stats.rsrq,
		.error_code = error_code,
		.iface = cellular_iface,
	};
	
	events_publish_cellular(&event);
	
	/* Call registered callback if available */
	if (event_callback) {
		event_callback(type, &event);
	}
}

int cellular_module_init(void)
{
	int ret;
	
	LOG_INF("Initializing cellular module");
	
	/* Initialize modem info library */
	ret = modem_info_init();
	if (ret) {
		LOG_ERR("Failed to initialize modem info: %d", ret);
		return ret;
	}
	
	/* Configure modem */
	ret = cellular_configure_modem();
	if (ret) {
		LOG_ERR("Failed to configure modem: %d", ret);
		return ret;
	}
	
	/* Initialize work items */
	k_work_init_delayable(&connect_work, connect_work_handler);
	k_work_init_delayable(&stats_work, stats_work_handler);
	k_work_init_delayable(&idle_timeout_work, idle_timeout_work_handler);
	
	/* Get cellular network interface */
	cellular_iface = utils_get_cellular_iface();
	if (!cellular_iface) {
		LOG_WRN("Cellular interface not found - will retry later");
	}
	
	current_state = CELLULAR_STATE_INITIALIZING;
	
	/* Start connection if auto-connect is enabled */
	if (config.auto_connect) {
		k_work_reschedule(&connect_work, K_SECONDS(2));
	}
	
	/* Start periodic statistics updates */
	k_work_reschedule(&stats_work, K_SECONDS(10));
	
	cellular_healthy = true;
	LOG_INF("Cellular module initialized successfully");
	return 0;
}

int cellular_connect(void)
{
	if (current_state == CELLULAR_STATE_CONNECTED) {
		return 0;
	}
	
	k_work_reschedule(&connect_work, K_NO_WAIT);
	return 0;
}

int cellular_disconnect(void)
{
	int ret;
	
	LOG_INF("Disconnecting from cellular network");
	
	ret = lte_lc_offline();
	if (ret) {
		LOG_ERR("Failed to go offline: %d", ret);
		return ret;
	}
	
	current_state = CELLULAR_STATE_DISCONNECTED;
	cellular_publish_event(CELLULAR_EVENT_DISCONNECTED, 0);
	
	return 0;
}

bool cellular_is_connected(void)
{
	return (current_state == CELLULAR_STATE_CONNECTED);
}

enum cellular_state cellular_get_state(void)
{
	return current_state;
}

bool cellular_module_is_healthy(void)
{
	return cellular_healthy;
}

int cellular_module_shutdown(void)
{
	LOG_INF("Shutting down cellular module");
	
	/* Cancel work items */
	k_work_cancel_delayable(&connect_work);
	k_work_cancel_delayable(&stats_work);
	k_work_cancel_delayable(&idle_timeout_work);
	
	/* Disconnect from network */
	cellular_disconnect();
	
	cellular_healthy = false;
	current_state = CELLULAR_STATE_DISABLED;
	
	LOG_INF("Cellular module shutdown completed");
	return 0;
}

struct net_if *cellular_get_interface(void)
{
	/* Return the cellular network interface */
	return net_if_get_default();
}

int cellular_enter_power_saving(void)
{
	LOG_INF("Entering cellular power saving mode");
	/* TODO: Implement power saving mode */
	return 0;
}
