/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_mgmt.h>
#include <zephyr/smf.h>
#include <zephyr/zbus/zbus.h>

#include <modem/lte_lc.h>
#include <modem/pdn.h>
#include <modem/modem_info.h>

#include "cellular_module.h"
#include "../events/router_events.h"

LOG_MODULE_REGISTER(cellular_module, CONFIG_LOG_DEFAULT_LEVEL);

/* Cellular state machine states */
enum cellular_state {
	CELLULAR_STATE_IDLE,
	CELLULAR_STATE_CONNECTING,
	CELLULAR_STATE_CONNECTED,
	CELLULAR_STATE_DISCONNECTING,
	CELLULAR_STATE_ERROR,
};

/* Cellular state machine context */
struct cellular_sm_ctx {
	struct smf_ctx ctx;
	struct net_if *iface;
	struct k_work_delayable connect_work;
	struct k_work_delayable status_work;
	int retry_count;
	int signal_strength;
	char operator_name[32];
	bool is_connected;
};

static struct cellular_sm_ctx cellular_ctx;

/* Forward declarations */
static void cellular_state_idle_entry(void *o);
static void cellular_state_idle_run(void *o);
static void cellular_state_connecting_entry(void *o);
static void cellular_state_connecting_run(void *o);
static void cellular_state_connected_entry(void *o);
static void cellular_state_connected_run(void *o);
static void cellular_state_disconnecting_entry(void *o);
static void cellular_state_disconnecting_run(void *o);
static void cellular_state_error_entry(void *o);
static void cellular_state_error_run(void *o);

/* State machine definition */
static const struct smf_state cellular_states[] = {
	[CELLULAR_STATE_IDLE] = SMF_CREATE_STATE(
		cellular_state_idle_entry,
		cellular_state_idle_run,
		NULL,
		NULL,
		NULL),
	[CELLULAR_STATE_CONNECTING] = SMF_CREATE_STATE(
		cellular_state_connecting_entry,
		cellular_state_connecting_run,
		NULL,
		NULL,
		NULL),
	[CELLULAR_STATE_CONNECTED] = SMF_CREATE_STATE(
		cellular_state_connected_entry,
		cellular_state_connected_run,
		NULL,
		NULL,
		NULL),
	[CELLULAR_STATE_DISCONNECTING] = SMF_CREATE_STATE(
		cellular_state_disconnecting_entry,
		cellular_state_disconnecting_run,
		NULL,
		NULL,
		NULL),
	[CELLULAR_STATE_ERROR] = SMF_CREATE_STATE(
		cellular_state_error_entry,
		cellular_state_error_run,
		NULL,
		NULL,
		NULL),
};

/* LTE event handler */
static void lte_handler(const struct lte_lc_evt *const evt)
{
	struct cellular_event cell_evt = {0};
	
	switch (evt->type) {
	case LTE_LC_EVT_NW_REG_STATUS:
		LOG_INF("LTE registration status: %d", evt->nw_reg_status);
		
		if ((evt->nw_reg_status == LTE_LC_NW_REG_REGISTERED_HOME) ||
		    (evt->nw_reg_status == LTE_LC_NW_REG_REGISTERED_ROAMING)) {
			cellular_ctx.is_connected = true;
			cell_evt.type = CELLULAR_EVENT_CONNECTED;
			smf_set_state(SMF_CTX(&cellular_ctx), &cellular_states[CELLULAR_STATE_CONNECTED]);
		} else {
			cellular_ctx.is_connected = false;
			cell_evt.type = CELLULAR_EVENT_DISCONNECTED;
			if (smf_get_state(SMF_CTX(&cellular_ctx)) == &cellular_states[CELLULAR_STATE_CONNECTED]) {
				smf_set_state(SMF_CTX(&cellular_ctx), &cellular_states[CELLULAR_STATE_CONNECTING]);
			}
		}
		
		cell_evt.iface = cellular_ctx.iface;
		strncpy(cell_evt.operator_name, cellular_ctx.operator_name, sizeof(cell_evt.operator_name) - 1);
		cell_evt.signal_strength = cellular_ctx.signal_strength;
		zbus_chan_pub(&cellular_chan, &cell_evt, K_MSEC(100));
		break;
		
	case LTE_LC_EVT_PSM_UPDATE:
		LOG_INF("PSM parameter update: TAU: %d, Active time: %d",
			evt->psm_cfg.tau, evt->psm_cfg.active_time);
		break;
		
	case LTE_LC_EVT_EDRX_UPDATE:
		LOG_INF("eDRX parameter update: eDRX: %f, PTW: %f",
			evt->edrx_cfg.edrx, evt->edrx_cfg.ptw);
		break;
		
	case LTE_LC_EVT_RRC_UPDATE:
		LOG_DBG("RRC mode: %s", evt->rrc_mode == LTE_LC_RRC_MODE_CONNECTED ?
			"Connected" : "Idle");
		break;
		
	case LTE_LC_EVT_CELL_UPDATE:
		LOG_DBG("LTE cell changed: Cell ID: 0x%08x, Tracking area: 0x%04x",
			evt->cell.id, evt->cell.tac);
		break;
		
	default:
		break;
	}
}

/* PDN event handler */
static void pdn_event_handler(uint8_t cid, enum pdn_event event, int reason)
{
	struct cellular_event cell_evt = {0};
	
	switch (event) {
	case PDN_EVENT_CNEC_ESM:
		LOG_INF("PDP context %d, %s", cid, reason == 0 ? "activated" : "deactivated");
		break;
		
	case PDN_EVENT_ACTIVATED:
		LOG_INF("PDN connection activated");
		break;
		
	case PDN_EVENT_DEACTIVATED:
		LOG_INF("PDN connection deactivated");
		break;
		
	case PDN_EVENT_NETWORK_DETACH:
		LOG_INF("Network detached");
		cellular_ctx.is_connected = false;
		cell_evt.type = CELLULAR_EVENT_DISCONNECTED;
		cell_evt.iface = cellular_ctx.iface;
		zbus_chan_pub(&cellular_chan, &cell_evt, K_MSEC(100));
		break;
		
	case PDN_EVENT_IPV6_UP:
		LOG_INF("PDN IPv6 up");
		break;
		
	case PDN_EVENT_IPV6_DOWN:
		LOG_INF("PDN IPv6 down");
		break;
		
	default:
		LOG_WRN("Unexpected PDN event: %d", event);
		break;
	}
}

/* Work handlers */
static void connect_work_handler(struct k_work *work)
{
	smf_set_state(SMF_CTX(&cellular_ctx), &cellular_states[CELLULAR_STATE_CONNECTING]);
}

static void status_work_handler(struct k_work *work)
{
	/* Update signal strength and operator info */
	int rsrp, rsrq;
	char operator[32];
	
	if (modem_info_get_rsrp(&rsrp) == 0) {
		cellular_ctx.signal_strength = rsrp;
	}
	
	if (modem_info_string_get(MODEM_INFO_OPERATOR, operator, sizeof(operator)) > 0) {
		strncpy(cellular_ctx.operator_name, operator, sizeof(cellular_ctx.operator_name) - 1);
	}
	
	/* Schedule next status update */
	k_work_schedule(&cellular_ctx.status_work, K_SECONDS(30));
}

/* State machine implementations */
static void cellular_state_idle_entry(void *o)
{
	LOG_INF("Cellular: Entering IDLE state");
	cellular_ctx.retry_count = 0;
}

static void cellular_state_idle_run(void *o)
{
	/* Stay in idle until explicitly started */
}

static void cellular_state_connecting_entry(void *o)
{
	LOG_INF("Cellular: Entering CONNECTING state");
	
	struct cellular_event evt = {
		.type = CELLULAR_EVENT_CONNECTING,
		.iface = cellular_ctx.iface
	};
	zbus_chan_pub(&cellular_chan, &evt, K_MSEC(100));
}

static void cellular_state_connecting_run(void *o)
{
	int ret;
	
	LOG_INF("Attempting to connect to LTE network (attempt %d)", cellular_ctx.retry_count + 1);
	
	ret = lte_lc_connect_async(lte_handler);
	if (ret) {
		LOG_ERR("Failed to start LTE connection: %d", ret);
		cellular_ctx.retry_count++;
		
		if (cellular_ctx.retry_count >= 3) {
			smf_set_state(SMF_CTX(&cellular_ctx), &cellular_states[CELLULAR_STATE_ERROR]);
		} else {
			/* Retry after delay */
			k_work_schedule(&cellular_ctx.connect_work, K_SECONDS(10));
		}
	}
}

static void cellular_state_connected_entry(void *o)
{
	LOG_INF("Cellular: Entering CONNECTED state");
	cellular_ctx.retry_count = 0;
	
	/* Start status monitoring */
	k_work_schedule(&cellular_ctx.status_work, K_SECONDS(5));
}

static void cellular_state_connected_run(void *o)
{
	/* Monitor connection status */
}

static void cellular_state_disconnecting_entry(void *o)
{
	LOG_INF("Cellular: Entering DISCONNECTING state");
	
	int ret = lte_lc_offline();
	if (ret) {
		LOG_ERR("Failed to disconnect: %d", ret);
	}
	
	smf_set_state(SMF_CTX(&cellular_ctx), &cellular_states[CELLULAR_STATE_IDLE]);
}

static void cellular_state_disconnecting_run(void *o)
{
	/* Transition handled in entry */
}

static void cellular_state_error_entry(void *o)
{
	LOG_ERR("Cellular: Entering ERROR state");
	
	struct cellular_event evt = {
		.type = CELLULAR_EVENT_ERROR,
		.error_code = -EIO,
		.iface = cellular_ctx.iface
	};
	zbus_chan_pub(&cellular_chan, &evt, K_MSEC(100));
}

static void cellular_state_error_run(void *o)
{
	/* Stay in error state until reset */
}

/* Public API implementations */
int cellular_module_init(void)
{
	int ret;

	LOG_INF("Initializing cellular module");

	/* Initialize state machine */
	smf_set_initial(SMF_CTX(&cellular_ctx), &cellular_states[CELLULAR_STATE_IDLE]);

	/* Initialize work items */
	k_work_init_delayable(&cellular_ctx.connect_work, connect_work_handler);
	k_work_init_delayable(&cellular_ctx.status_work, status_work_handler);

	/* Initialize modem info */
	ret = modem_info_init();
	if (ret) {
		LOG_ERR("Failed to initialize modem info: %d", ret);
		return ret;
	}

	/* Register PDN event handler */
	ret = pdn_default_ctx_cb_reg(pdn_event_handler);
	if (ret) {
		LOG_ERR("Failed to register PDN callback: %d", ret);
		return ret;
	}

	/* Get cellular network interface */
	cellular_ctx.iface = net_if_get_default();
	if (!cellular_ctx.iface) {
		LOG_ERR("Failed to get default network interface");
		return -ENODEV;
	}

	LOG_INF("Cellular module initialized");
	return 0;
}

int cellular_module_start(void)
{
	LOG_INF("Starting cellular module");

	/* Trigger connection */
	k_work_schedule(&cellular_ctx.connect_work, K_NO_WAIT);

	return 0;
}

int cellular_module_stop(void)
{
	LOG_INF("Stopping cellular module");

	/* Cancel any pending work */
	k_work_cancel_delayable(&cellular_ctx.connect_work);
	k_work_cancel_delayable(&cellular_ctx.status_work);

	/* Transition to disconnecting state */
	smf_set_state(SMF_CTX(&cellular_ctx), &cellular_states[CELLULAR_STATE_DISCONNECTING]);

	return 0;
}

struct net_if *cellular_module_get_iface(void)
{
	return cellular_ctx.iface;
}

bool cellular_module_is_connected(void)
{
	return cellular_ctx.is_connected;
}

int cellular_module_get_signal_strength(void)
{
	return cellular_ctx.signal_strength;
}

int cellular_module_get_operator(char *buf, size_t buf_size)
{
	if (!buf || buf_size == 0) {
		return -EINVAL;
	}

	strncpy(buf, cellular_ctx.operator_name, buf_size - 1);
	buf[buf_size - 1] = '\0';

	return 0;
}
