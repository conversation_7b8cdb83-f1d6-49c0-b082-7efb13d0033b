/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef UI_MODULE_H_
#define UI_MODULE_H_

#include <zephyr/kernel.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief LED states for status indication
 */
enum led_state {
	LED_STATE_OFF,
	LED_STATE_ON,
	LED_STATE_BLINK_SLOW,
	LED_STATE_BLINK_FAST,
	LED_STATE_PULSE,
};

/**
 * @brief LED identifiers
 */
enum led_id {
	LED_CELLULAR = CONFIG_CELLULAR_WIFI_ROUTER_LED_CELLULAR,
	LED_WIFI = CONFIG_CELLULAR_WIFI_ROUTER_LED_WIFI,
	LED_BRIDGE = CONFIG_CELLULAR_WIFI_ROUTER_LED_BRIDGE,
	LED_POWER = CONFIG_CELLULAR_WIFI_ROUTER_LED_POWER,
};

/**
 * @brief Button identifiers
 */
enum button_id {
	BUTTON_CONFIG = CONFIG_CELLULAR_WIFI_ROUTER_BUTTON_CONFIG,
	BUTTON_RESET = CONFIG_CELLULAR_WIFI_ROUTER_BUTTON_RESET,
};

/**
 * @brief Initialize the UI module
 * 
 * @return 0 on success, negative error code on failure
 */
int ui_module_init(void);

/**
 * @brief Set LED state
 * 
 * @param led_id LED identifier
 * @param state LED state
 * @return 0 on success, negative error code on failure
 */
int ui_module_set_led(enum led_id led_id, enum led_state state);

/**
 * @brief Get button state
 * 
 * @param button_id Button identifier
 * @return true if pressed, false otherwise
 */
bool ui_module_get_button(enum button_id button_id);

/**
 * @brief Enable or disable UI (for power saving)
 * 
 * @param enable true to enable, false to disable
 * @return 0 on success, negative error code on failure
 */
int ui_module_enable(bool enable);

/**
 * @brief Check if UI is enabled
 * 
 * @return true if enabled, false otherwise
 */
bool ui_module_is_enabled(void);

#ifdef __cplusplus
}
#endif

#endif /* UI_MODULE_H_ */
