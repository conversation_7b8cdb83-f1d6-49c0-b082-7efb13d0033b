/*
 * Cellular WiFi Router - UI Module Header
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef UI_MODULE_H
#define UI_MODULE_H

#include <zephyr/kernel.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/pwm.h>
#include "../common/events.h"

#ifdef __cplusplus
extern "C" {
#endif

/* LED pattern definitions */
enum led_pattern {
	LED_PATTERN_OFF,
	LED_PATTERN_ON,
	LED_PATTERN_SLOW_BLINK,
	LED_PATTERN_FAST_BLINK,
	LED_PATTERN_PULSE,
	LED_PATTERN_DOUBLE_BLINK,
};

/* LED IDs */
enum led_id {
	LED_STATUS = 0,
	LED_CELLULAR = 1,
	LED_WIFI = 2,
	LED_COUNT
};

/* Button IDs */
enum button_id {
	BUTTON_CONTROL = 0,
	BUTTON_COUNT
};

/* UI States */
enum ui_state {
	UI_STATE_DISABLED,
	UI_STATE_INITIALIZING,
	UI_STATE_IDLE,
	UI_STATE_CELLULAR_CONNECTING,
	UI_STATE_WIFI_STARTING,
	UI_STATE_ROUTER_ACTIVE,
	UI_STATE_ERROR,
	UI_STATE_LOW_POWER,
};

/* LED Control Structure */
struct led_control {
	const struct device *gpio_dev;
	const struct device *pwm_dev;
	uint32_t gpio_pin;
	uint32_t pwm_channel;
	bool gpio_active_low;
	enum led_pattern current_pattern;
	uint8_t brightness;
	bool enabled;
	struct k_work_delayable pattern_work;
};

/* Button Control Structure */
struct button_control {
	const struct device *gpio_dev;
	uint32_t gpio_pin;
	struct gpio_callback gpio_cb;
	bool enabled;
	bool pressed;
	int64_t last_press_time;
	int64_t last_release_time;
};

/* UI Module Functions */
int ui_module_init(void);
int ui_module_shutdown(void);
bool ui_module_is_healthy(void);

/* UI Control Functions */
int ui_enable(bool enable);
int ui_set_state(enum ui_state state);
enum ui_state ui_get_state(void);

/* LED Control Functions */
int ui_led_set_pattern(enum led_id led, enum led_pattern pattern);
int ui_led_set_brightness(enum led_id led, uint8_t brightness);
int ui_led_set_state(enum led_id led, bool on);
int ui_led_enable(enum led_id led, bool enable);

/* Button Control Functions */
int ui_button_enable(enum button_id button, bool enable);
bool ui_button_is_pressed(enum button_id button);

/* Power Management Functions */
int ui_enter_low_power_mode(void);
int ui_exit_low_power_mode(void);

/* Configuration Functions */
int ui_load_config(void);
int ui_save_config(void);

#ifdef __cplusplus
}
#endif

#endif /* UI_MODULE_H */
