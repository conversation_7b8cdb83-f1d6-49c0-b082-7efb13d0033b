/*
 * Cellular WiFi Router - WiFi Module Implementation
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include "wifi_module.h"
#include "../common/events.h"
#include "../common/utils.h"
#include <zephyr/logging/log.h>
#include <zephyr/zbus/zbus.h>
#include <zephyr/smf.h>
#include <zephyr/net/net_mgmt.h>
#include <zephyr/net/net_event.h>
#include <zephyr/net/wifi_mgmt.h>
#include <zephyr/net/dhcpv4_server.h>

LOG_MODULE_REGISTER(wifi_module, CONFIG_LOG_DEFAULT_LEVEL);

/* State Machine Context */
static struct smf_ctx wifi_smf_ctx;
static enum wifi_state current_state = WIFI_STATE_DISABLED;
static bool wifi_healthy = false;

/* WiFi AP Configuration */
static struct wifi_ap_config ap_config = {
	.ssid = "Thingy91X-Router",
	.password = "Nordic123",
	.channel = 6,
	.security = WIFI_SECURITY_TYPE_PSK,
	.max_clients = 10,
	.hidden = false,
	.beacon_interval = 100,
	.dtim_period = 2,
};

/* DHCP Server Configuration */
static struct dhcp_server_config dhcp_config = {
	.pool_start = "192.168.4.2",
	.pool_end = "192.168.4.100",
	.gateway = "192.168.4.1",
	.netmask = "255.255.255.0",
	.dns_primary = "8.8.8.8",
	.dns_secondary = "8.8.4.4",
	.lease_time_sec = 3600,
};

/* Module Statistics */
static struct wifi_stats stats = {0};

/* Network Interface */
static struct net_if *wifi_iface = NULL;

/* Client tracking */
static struct wifi_client_info clients[10];
static uint8_t client_count = 0;

/* Work items */
static struct k_work_delayable start_ap_work;
static struct k_work_delayable stats_work;

/* Event callback */
static wifi_event_callback_t event_callback = NULL;

/* Network management callback */
static struct net_mgmt_event_callback wifi_mgmt_cb;

/* ZBUS Observers */
ZBUS_SUBSCRIBER_DEFINE(wifi_power_events_sub, 4);

/* Forward declarations */
static void wifi_mgmt_event_handler(struct net_mgmt_event_callback *cb,
				    uint32_t mgmt_event, struct net_if *iface);
static void start_ap_work_handler(struct k_work *work);
static void stats_work_handler(struct k_work *work);
static int wifi_configure_interface(void);
static int wifi_start_dhcp_server_internal(void);
static void wifi_publish_event(enum wifi_event_type type, int error_code);

/* WiFi management event handler */
static void wifi_mgmt_event_handler(struct net_mgmt_event_callback *cb,
				    uint32_t mgmt_event, struct net_if *iface)
{
	switch (mgmt_event) {
	case NET_EVENT_WIFI_AP_ENABLE_RESULT:
		LOG_INF("WiFi AP enable result");
		if (iface == wifi_iface) {
			current_state = WIFI_STATE_AP_ACTIVE;
			wifi_publish_event(WIFI_EVENT_AP_STARTED, 0);
			
			/* Start DHCP server */
			wifi_start_dhcp_server_internal();
		}
		break;
		
	case NET_EVENT_WIFI_AP_DISABLE_RESULT:
		LOG_INF("WiFi AP disable result");
		if (iface == wifi_iface) {
			current_state = WIFI_STATE_DISABLED;
			wifi_publish_event(WIFI_EVENT_AP_STOPPED, 0);
		}
		break;
		
	case NET_EVENT_WIFI_AP_STA_CONNECTED:
		LOG_INF("WiFi client connected");
		if (iface == wifi_iface) {
			client_count++;
			stats.connected_clients = client_count;
			stats.total_connections++;
			wifi_publish_event(WIFI_EVENT_CLIENT_CONNECTED, 0);
		}
		break;
		
	case NET_EVENT_WIFI_AP_STA_DISCONNECTED:
		LOG_INF("WiFi client disconnected");
		if (iface == wifi_iface) {
			if (client_count > 0) {
				client_count--;
			}
			stats.connected_clients = client_count;
			stats.total_disconnections++;
			wifi_publish_event(WIFI_EVENT_CLIENT_DISCONNECTED, 0);
		}
		break;
		
	default:
		break;
	}
}

/* Work handlers */
static void start_ap_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	int ret;
	struct wifi_connect_req_params params = {0};
	
	LOG_INF("Starting WiFi Access Point");
	current_state = WIFI_STATE_STARTING_AP;
	wifi_publish_event(WIFI_EVENT_AP_STARTING, 0);
	
	/* Configure AP parameters */
	params.ssid = ap_config.ssid;
	params.ssid_length = strlen(ap_config.ssid);
	params.psk = ap_config.password;
	params.psk_length = strlen(ap_config.password);
	params.channel = ap_config.channel;
	params.security = ap_config.security;
	
	/* Enable AP mode */
	ret = net_mgmt(NET_REQUEST_WIFI_AP_ENABLE, wifi_iface, &params,
		       sizeof(struct wifi_connect_req_params));
	if (ret) {
		LOG_ERR("Failed to enable WiFi AP: %d", ret);
		current_state = WIFI_STATE_AP_ERROR;
		wifi_publish_event(WIFI_EVENT_AP_ERROR, ret);
		return;
	}
	
	LOG_INF("WiFi AP enable request sent");
}

static void stats_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	/* Update statistics */
	stats.current_state = current_state;
	stats.current_channel = ap_config.channel;
	
	if (current_state == WIFI_STATE_AP_ACTIVE) {
		stats.ap_uptime_sec++;
	}
	
	/* Reschedule stats work */
	k_work_reschedule(&stats_work, K_SECONDS(1));
}

static int wifi_configure_interface(void)
{
	int ret;
	struct in_addr addr, netmask;
	
	if (!wifi_iface) {
		LOG_ERR("WiFi interface not available");
		return -ENODEV;
	}
	
	/* Configure IP address for AP */
	ret = utils_str_to_ip(dhcp_config.gateway, &addr);
	if (ret) {
		LOG_ERR("Invalid gateway IP address: %s", dhcp_config.gateway);
		return ret;
	}
	
	ret = utils_str_to_ip(dhcp_config.netmask, &netmask);
	if (ret) {
		LOG_ERR("Invalid netmask: %s", dhcp_config.netmask);
		return ret;
	}
	
	/* Set interface IP address */
	struct net_if_addr *if_addr = net_if_ipv4_addr_add(wifi_iface, &addr, 
							    NET_ADDR_MANUAL, 0);
	if (!if_addr) {
		LOG_ERR("Failed to add IP address to WiFi interface");
		return -ENOMEM;
	}
	
	/* Bring interface up */
	net_if_up(wifi_iface);
	
	LOG_INF("WiFi interface configured: %s", dhcp_config.gateway);
	return 0;
}

static int wifi_start_dhcp_server_internal(void)
{
	int ret;
	struct in_addr pool_start;
	
	LOG_INF("Starting DHCP server");
	
	ret = utils_str_to_ip(dhcp_config.pool_start, &pool_start);
	if (ret) {
		LOG_ERR("Invalid DHCP pool start address: %s", dhcp_config.pool_start);
		return ret;
	}
	
	ret = net_dhcpv4_server_start(wifi_iface, &pool_start);
	if (ret == -EALREADY) {
		LOG_INF("DHCP server already running");
		return 0;
	} else if (ret < 0) {
		LOG_ERR("Failed to start DHCP server: %d", ret);
		return ret;
	}
	
	LOG_INF("DHCP server started successfully");
	return 0;
}

static void wifi_publish_event(enum wifi_event_type type, int error_code)
{
	struct wifi_event event = {
		.type = type,
		.iface = wifi_iface,
		.client_count = client_count,
		.error_code = error_code,
	};
	
	events_publish_wifi(&event);
	
	/* Call registered callback if available */
	if (event_callback) {
		event_callback(type, &event);
	}
}

int wifi_module_init(void)
{
	int ret;
	
	LOG_INF("Initializing WiFi module");
	
	/* Get WiFi network interface */
	wifi_iface = net_if_get_first_wifi();
	if (!wifi_iface) {
		LOG_ERR("WiFi interface not found");
		return -ENODEV;
	}
	
	/* Configure network interface */
	ret = wifi_configure_interface();
	if (ret) {
		LOG_ERR("Failed to configure WiFi interface: %d", ret);
		return ret;
	}
	
	/* Register network management callback */
	net_mgmt_init_event_callback(&wifi_mgmt_cb, wifi_mgmt_event_handler,
				     NET_EVENT_WIFI_AP_ENABLE_RESULT |
				     NET_EVENT_WIFI_AP_DISABLE_RESULT |
				     NET_EVENT_WIFI_AP_STA_CONNECTED |
				     NET_EVENT_WIFI_AP_STA_DISCONNECTED);
	
	net_mgmt_add_event_callback(&wifi_mgmt_cb);
	
	/* Initialize work items */
	k_work_init_delayable(&start_ap_work, start_ap_work_handler);
	k_work_init_delayable(&stats_work, stats_work_handler);
	
	/* Initialize client tracking */
	memset(clients, 0, sizeof(clients));
	client_count = 0;
	
	current_state = WIFI_STATE_INITIALIZING;
	
	/* Start AP automatically */
	k_work_reschedule(&start_ap_work, K_SECONDS(1));
	
	/* Start periodic statistics updates */
	k_work_reschedule(&stats_work, K_SECONDS(5));
	
	wifi_healthy = true;
	LOG_INF("WiFi module initialized successfully");
	return 0;
}

int wifi_start_ap(void)
{
	if (current_state == WIFI_STATE_AP_ACTIVE) {
		return 0;
	}
	
	k_work_reschedule(&start_ap_work, K_NO_WAIT);
	return 0;
}

int wifi_stop_ap(void)
{
	int ret;
	
	if (current_state != WIFI_STATE_AP_ACTIVE) {
		return 0;
	}
	
	LOG_INF("Stopping WiFi Access Point");
	
	ret = net_mgmt(NET_REQUEST_WIFI_AP_DISABLE, wifi_iface, NULL, 0);
	if (ret) {
		LOG_ERR("Failed to disable WiFi AP: %d", ret);
		return ret;
	}
	
	return 0;
}

bool wifi_is_ap_active(void)
{
	return (current_state == WIFI_STATE_AP_ACTIVE);
}

enum wifi_state wifi_get_state(void)
{
	return current_state;
}

struct net_if *wifi_get_interface(void)
{
	return wifi_iface;
}

int wifi_get_client_count(void)
{
	return client_count;
}

bool wifi_module_is_healthy(void)
{
	return wifi_healthy;
}

int wifi_module_shutdown(void)
{
	LOG_INF("Shutting down WiFi module");
	
	/* Cancel work items */
	k_work_cancel_delayable(&start_ap_work);
	k_work_cancel_delayable(&stats_work);
	
	/* Stop AP */
	wifi_stop_ap();
	
	/* Remove network management callback */
	net_mgmt_del_event_callback(&wifi_mgmt_cb);
	
	wifi_healthy = false;
	current_state = WIFI_STATE_DISABLED;
	
	LOG_INF("WiFi module shutdown completed");
	return 0;
}
