/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_mgmt.h>
#include <zephyr/net/wifi_mgmt.h>
#include <zephyr/net/dhcpv4_server.h>
#include <zephyr/smf.h>
#include <zephyr/zbus/zbus.h>

#include "wifi_module.h"
#include "../events/router_events.h"

LOG_MODULE_REGISTER(wifi_module, CONFIG_LOG_DEFAULT_LEVEL);

/* WiFi state machine states */
enum wifi_state {
	WIFI_STATE_IDLE,
	WIFI_STATE_STARTING_AP,
	WIFI_STATE_AP_ENABLED,
	WIFI_STATE_STOPPING_AP,
	WIFI_STATE_ERROR,
};

/* WiFi state machine context */
struct wifi_sm_ctx {
	struct smf_ctx ctx;
	struct net_if *iface;
	struct k_work_delayable start_work;
	struct k_work_delayable dhcp_work;
	struct net_mgmt_event_callback wifi_cb;
	struct net_mgmt_event_callback net_cb;
	int client_count;
	bool ap_enabled;
	char ssid[32];
	char password[64];
	int channel;
};

static struct wifi_sm_ctx wifi_ctx;

/* Forward declarations */
static void wifi_state_idle_entry(void *o);
static void wifi_state_idle_run(void *o);
static void wifi_state_starting_ap_entry(void *o);
static void wifi_state_starting_ap_run(void *o);
static void wifi_state_ap_enabled_entry(void *o);
static void wifi_state_ap_enabled_run(void *o);
static void wifi_state_stopping_ap_entry(void *o);
static void wifi_state_stopping_ap_run(void *o);
static void wifi_state_error_entry(void *o);
static void wifi_state_error_run(void *o);

/* State machine definition */
static const struct smf_state wifi_states[] = {
	[WIFI_STATE_IDLE] = SMF_CREATE_STATE(
		wifi_state_idle_entry,
		wifi_state_idle_run,
		NULL,
		NULL,
		NULL),
	[WIFI_STATE_STARTING_AP] = SMF_CREATE_STATE(
		wifi_state_starting_ap_entry,
		wifi_state_starting_ap_run,
		NULL,
		NULL,
		NULL),
	[WIFI_STATE_AP_ENABLED] = SMF_CREATE_STATE(
		wifi_state_ap_enabled_entry,
		wifi_state_ap_enabled_run,
		NULL,
		NULL,
		NULL),
	[WIFI_STATE_STOPPING_AP] = SMF_CREATE_STATE(
		wifi_state_stopping_ap_entry,
		wifi_state_stopping_ap_run,
		NULL,
		NULL,
		NULL),
	[WIFI_STATE_ERROR] = SMF_CREATE_STATE(
		wifi_state_error_entry,
		wifi_state_error_run,
		NULL,
		NULL,
		NULL),
};

/* WiFi management event handler */
static void wifi_mgmt_event_handler(struct net_mgmt_event_callback *cb,
				   uint32_t mgmt_event, struct net_if *iface)
{
	struct wifi_event wifi_evt = {0};
	
	switch (mgmt_event) {
	case NET_EVENT_WIFI_AP_ENABLE_RESULT:
		LOG_INF("WiFi AP enable result");
		wifi_ctx.ap_enabled = true;
		wifi_evt.type = WIFI_EVENT_AP_ENABLED;
		wifi_evt.iface = iface;
		strncpy(wifi_evt.ssid, wifi_ctx.ssid, sizeof(wifi_evt.ssid) - 1);
		zbus_chan_pub(&wifi_chan, &wifi_evt, K_MSEC(100));
		smf_set_state(SMF_CTX(&wifi_ctx), &wifi_states[WIFI_STATE_AP_ENABLED]);
		break;
		
	case NET_EVENT_WIFI_AP_DISABLE_RESULT:
		LOG_INF("WiFi AP disable result");
		wifi_ctx.ap_enabled = false;
		wifi_evt.type = WIFI_EVENT_AP_DISABLED;
		wifi_evt.iface = iface;
		zbus_chan_pub(&wifi_chan, &wifi_evt, K_MSEC(100));
		smf_set_state(SMF_CTX(&wifi_ctx), &wifi_states[WIFI_STATE_IDLE]);
		break;
		
	case NET_EVENT_WIFI_AP_STA_CONNECTED:
		LOG_INF("WiFi client connected");
		wifi_ctx.client_count++;
		wifi_evt.type = WIFI_EVENT_CLIENT_CONNECTED;
		wifi_evt.iface = iface;
		wifi_evt.client_count = wifi_ctx.client_count;
		zbus_chan_pub(&wifi_chan, &wifi_evt, K_MSEC(100));
		break;
		
	case NET_EVENT_WIFI_AP_STA_DISCONNECTED:
		LOG_INF("WiFi client disconnected");
		if (wifi_ctx.client_count > 0) {
			wifi_ctx.client_count--;
		}
		wifi_evt.type = WIFI_EVENT_CLIENT_DISCONNECTED;
		wifi_evt.iface = iface;
		wifi_evt.client_count = wifi_ctx.client_count;
		zbus_chan_pub(&wifi_chan, &wifi_evt, K_MSEC(100));
		break;
		
	default:
		LOG_DBG("Unhandled WiFi event: 0x%08X", mgmt_event);
		break;
	}
}

/* Network management event handler */
static void net_mgmt_event_handler(struct net_mgmt_event_callback *cb,
				  uint32_t mgmt_event, struct net_if *iface)
{
	switch (mgmt_event) {
	case NET_EVENT_IF_UP:
		if (iface == wifi_ctx.iface) {
			LOG_INF("WiFi interface up");
		}
		break;
		
	case NET_EVENT_IF_DOWN:
		if (iface == wifi_ctx.iface) {
			LOG_INF("WiFi interface down");
		}
		break;
		
	default:
		break;
	}
}

/* Work handlers */
static void start_work_handler(struct k_work *work)
{
	smf_set_state(SMF_CTX(&wifi_ctx), &wifi_states[WIFI_STATE_STARTING_AP]);
}

static void dhcp_work_handler(struct k_work *work)
{
	int ret;
	struct in_addr pool_start;
	
	/* Configure DHCP server */
	ret = net_addr_pton(AF_INET, CONFIG_CELLULAR_WIFI_ROUTER_DHCP_POOL_START, &pool_start);
	if (ret < 0) {
		LOG_ERR("Invalid DHCP pool start address");
		return;
	}
	
	ret = net_dhcpv4_server_start(wifi_ctx.iface, &pool_start);
	if (ret == -EALREADY) {
		LOG_INF("DHCP server already running");
	} else if (ret < 0) {
		LOG_ERR("Failed to start DHCP server: %d", ret);
	} else {
		LOG_INF("DHCP server started, pool starts from %s",
			CONFIG_CELLULAR_WIFI_ROUTER_DHCP_POOL_START);
	}
}

/* State machine implementations */
static void wifi_state_idle_entry(void *o)
{
	LOG_INF("WiFi: Entering IDLE state");
	wifi_ctx.client_count = 0;
	wifi_ctx.ap_enabled = false;
}

static void wifi_state_idle_run(void *o)
{
	/* Stay in idle until explicitly started */
}

static void wifi_state_starting_ap_entry(void *o)
{
	LOG_INF("WiFi: Entering STARTING_AP state");
	
	struct wifi_event evt = {
		.type = WIFI_EVENT_AP_ENABLING,
		.iface = wifi_ctx.iface
	};
	zbus_chan_pub(&wifi_chan, &evt, K_MSEC(100));
}

static void wifi_state_starting_ap_run(void *o)
{
	int ret;
	struct wifi_connect_req_params params = {0};
	
	LOG_INF("Starting WiFi AP: SSID=%s, Channel=%d", wifi_ctx.ssid, wifi_ctx.channel);
	
	/* Configure AP parameters */
	params.ssid = wifi_ctx.ssid;
	params.ssid_length = strlen(wifi_ctx.ssid);
	params.psk = wifi_ctx.password;
	params.psk_length = strlen(wifi_ctx.password);
	params.channel = wifi_ctx.channel;
	params.security = WIFI_SECURITY_TYPE_PSK;
	params.band = WIFI_FREQ_BAND_2_4_GHZ;
	
	ret = net_mgmt(NET_REQUEST_WIFI_AP_ENABLE, wifi_ctx.iface,
		       &params, sizeof(params));
	if (ret) {
		LOG_ERR("Failed to enable WiFi AP: %d", ret);
		smf_set_state(SMF_CTX(&wifi_ctx), &wifi_states[WIFI_STATE_ERROR]);
	}
}

static void wifi_state_ap_enabled_entry(void *o)
{
	LOG_INF("WiFi: Entering AP_ENABLED state");
	
	/* Start DHCP server */
	k_work_schedule(&wifi_ctx.dhcp_work, K_SECONDS(2));
}

static void wifi_state_ap_enabled_run(void *o)
{
	/* Monitor AP status */
}

static void wifi_state_stopping_ap_entry(void *o)
{
	LOG_INF("WiFi: Entering STOPPING_AP state");
	
	int ret = net_mgmt(NET_REQUEST_WIFI_AP_DISABLE, wifi_ctx.iface, NULL, 0);
	if (ret) {
		LOG_ERR("Failed to disable WiFi AP: %d", ret);
	}
}

static void wifi_state_stopping_ap_run(void *o)
{
	/* Transition handled by event */
}

static void wifi_state_error_entry(void *o)
{
	LOG_ERR("WiFi: Entering ERROR state");
	
	struct wifi_event evt = {
		.type = WIFI_EVENT_AP_ERROR,
		.error_code = -EIO,
		.iface = wifi_ctx.iface
	};
	zbus_chan_pub(&wifi_chan, &evt, K_MSEC(100));
}

static void wifi_state_error_run(void *o)
{
	/* Stay in error state until reset */
}

/* Public API implementations */
int wifi_module_init(void)
{
	LOG_INF("Initializing WiFi module");

	/* Initialize state machine */
	smf_set_initial(SMF_CTX(&wifi_ctx), &wifi_states[WIFI_STATE_IDLE]);

	/* Initialize work items */
	k_work_init_delayable(&wifi_ctx.start_work, start_work_handler);
	k_work_init_delayable(&wifi_ctx.dhcp_work, dhcp_work_handler);

	/* Get WiFi network interface */
	wifi_ctx.iface = net_if_get_first_wifi();
	if (!wifi_ctx.iface) {
		LOG_ERR("Failed to get WiFi interface");
		return -ENODEV;
	}

	/* Set up configuration */
	strncpy(wifi_ctx.ssid, CONFIG_CELLULAR_WIFI_ROUTER_WIFI_SSID, sizeof(wifi_ctx.ssid) - 1);
	strncpy(wifi_ctx.password, CONFIG_CELLULAR_WIFI_ROUTER_WIFI_PASSWORD, sizeof(wifi_ctx.password) - 1);
	wifi_ctx.channel = CONFIG_CELLULAR_WIFI_ROUTER_WIFI_CHANNEL;

	/* Register event callbacks */
	net_mgmt_init_event_callback(&wifi_ctx.wifi_cb, wifi_mgmt_event_handler,
				     NET_EVENT_WIFI_AP_ENABLE_RESULT |
				     NET_EVENT_WIFI_AP_DISABLE_RESULT |
				     NET_EVENT_WIFI_AP_STA_CONNECTED |
				     NET_EVENT_WIFI_AP_STA_DISCONNECTED);
	net_mgmt_add_event_callback(&wifi_ctx.wifi_cb);

	net_mgmt_init_event_callback(&wifi_ctx.net_cb, net_mgmt_event_handler,
				     NET_EVENT_IF_UP | NET_EVENT_IF_DOWN);
	net_mgmt_add_event_callback(&wifi_ctx.net_cb);

	LOG_INF("WiFi module initialized");
	return 0;
}

int wifi_module_start(void)
{
	LOG_INF("Starting WiFi module");

	/* Trigger AP start */
	k_work_schedule(&wifi_ctx.start_work, K_NO_WAIT);

	return 0;
}

int wifi_module_stop(void)
{
	LOG_INF("Stopping WiFi module");

	/* Cancel any pending work */
	k_work_cancel_delayable(&wifi_ctx.start_work);
	k_work_cancel_delayable(&wifi_ctx.dhcp_work);

	/* Transition to stopping state */
	smf_set_state(SMF_CTX(&wifi_ctx), &wifi_states[WIFI_STATE_STOPPING_AP]);

	return 0;
}

struct net_if *wifi_module_get_iface(void)
{
	return wifi_ctx.iface;
}

bool wifi_module_is_ap_enabled(void)
{
	return wifi_ctx.ap_enabled;
}

int wifi_module_get_client_count(void)
{
	return wifi_ctx.client_count;
}

int wifi_module_get_ssid(char *buf, size_t buf_size)
{
	if (!buf || buf_size == 0) {
		return -EINVAL;
	}

	strncpy(buf, wifi_ctx.ssid, buf_size - 1);
	buf[buf_size - 1] = '\0';

	return 0;
}
