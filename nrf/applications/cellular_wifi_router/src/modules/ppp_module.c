/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include "ppp_module.h"
#include "at_host_module.h"
#include "../common/events.h"
#include "../common/utils.h"
#include <zephyr/logging/log.h>
#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>
#include <zephyr/posix/sys/socket.h>
#include <modem/lte_lc.h>
/* #include <modem/pdn.h> */
#include <string.h>

LOG_MODULE_REGISTER(ppp_module, CONFIG_LOG_DEFAULT_LEVEL);

/* PPP module state */
static enum ppp_module_state ppp_state = PPP_MODULE_STATE_STOPPED;
static bool module_initialized = false;
static bool ppp_active = false;
static bool ppp_connected = false;
static bool cgev_forwarding = false;

/* PPP configuration */
static struct ppp_module_config ppp_config = {
	.auto_start = false,
	.mtu = 1500,
	.mru = 1500,
	.ipcp_enabled = true,
	.ipv6cp_enabled = false,
	.local_ip = "***********",
	.remote_ip = "***********",
	.dns_primary = "*******",
	.dns_secondary = "*******"
};

/* PPP statistics */
static struct {
	uint64_t tx_bytes;
	uint64_t rx_bytes;
	uint64_t tx_packets;
	uint64_t rx_packets;
} ppp_stats = {0};

/* PPP interface and buffers */
/* static struct net_if *ppp_iface; */
/* static uint8_t ppp_data_buf[1500]; */

/* Work items */
static struct k_work ppp_start_work;
static struct k_work ppp_stop_work;

/* Mutexes */
K_MUTEX_DEFINE(ppp_mutex);

/* Forward declarations */
static void ppp_start_work_fn(struct k_work *work);
static void ppp_stop_work_fn(struct k_work *work);
static int ppp_setup_interface(void);
static void ppp_cleanup_interface(void);

/* Work function to start PPP */
static void ppp_start_work_fn(struct k_work *work)
{
	int ret;
	
	LOG_INF("Starting PPP service");
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	
	if (ppp_state != PPP_MODULE_STATE_STOPPED) {
		LOG_WRN("PPP already starting or running");
		k_mutex_unlock(&ppp_mutex);
		return;
	}
	
	ppp_state = PPP_MODULE_STATE_STARTING;
	k_mutex_unlock(&ppp_mutex);
	
	/* Setup PPP interface */
	ret = ppp_setup_interface();
	if (ret) {
		LOG_ERR("Failed to setup PPP interface: %d", ret);
		k_mutex_lock(&ppp_mutex, K_FOREVER);
		ppp_state = PPP_MODULE_STATE_ERROR;
		k_mutex_unlock(&ppp_mutex);
		return;
	}
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	ppp_state = PPP_MODULE_STATE_RUNNING;
	ppp_active = true;
	k_mutex_unlock(&ppp_mutex);
	
	LOG_INF("PPP service started successfully");

	/* TODO: Send PPP started event via ZBUS */
}

/* Work function to stop PPP */
static void ppp_stop_work_fn(struct k_work *work)
{
	LOG_INF("Stopping PPP service");
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	
	if (ppp_state == PPP_MODULE_STATE_STOPPED) {
		LOG_WRN("PPP already stopped");
		k_mutex_unlock(&ppp_mutex);
		return;
	}
	
	ppp_state = PPP_MODULE_STATE_STOPPING;
	k_mutex_unlock(&ppp_mutex);
	
	/* Cleanup PPP interface */
	ppp_cleanup_interface();
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	ppp_state = PPP_MODULE_STATE_STOPPED;
	ppp_active = false;
	ppp_connected = false;
	k_mutex_unlock(&ppp_mutex);
	
	LOG_INF("PPP service stopped");

	/* TODO: Send PPP stopped event via ZBUS */
}

/* Setup PPP interface */
static int ppp_setup_interface(void)
{
	/* TODO: Implement PPP interface setup */
	LOG_INF("Setting up PPP interface");
	
	/* For now, just simulate successful setup */
	ppp_connected = true;
	
	return 0;
}

/* Cleanup PPP interface */
static void ppp_cleanup_interface(void)
{
	/* TODO: Implement PPP interface cleanup */
	LOG_INF("Cleaning up PPP interface");
	
	ppp_connected = false;
}

/* Initialize PPP module */
int ppp_module_init(void)
{
	if (module_initialized) {
		LOG_WRN("PPP module already initialized");
		return 0;
	}
	
	LOG_INF("Initializing PPP module");
	
	/* Initialize work items */
	k_work_init(&ppp_start_work, ppp_start_work_fn);
	k_work_init(&ppp_stop_work, ppp_stop_work_fn);
	
	/* Reset statistics */
	memset(&ppp_stats, 0, sizeof(ppp_stats));
	
	module_initialized = true;
	LOG_INF("PPP module initialized successfully");
	
	return 0;
}

/* Shutdown PPP module */
int ppp_module_shutdown(void)
{
	if (!module_initialized) {
		return 0;
	}
	
	LOG_INF("Shutting down PPP module");
	
	/* Stop PPP if running */
	if (ppp_active) {
		ppp_module_stop();
	}
	
	module_initialized = false;
	LOG_INF("PPP module shutdown completed");
	
	return 0;
}

/* Check if PPP module is healthy */
bool ppp_module_is_healthy(void)
{
	return module_initialized && (ppp_state != PPP_MODULE_STATE_ERROR);
}

/* Start PPP service */
int ppp_module_start(void)
{
	if (!module_initialized) {
		LOG_ERR("PPP module not initialized");
		return -ENODEV;
	}
	
	k_work_submit(&ppp_start_work);
	return 0;
}

/* Stop PPP service */
int ppp_module_stop(void)
{
	if (!module_initialized) {
		LOG_ERR("PPP module not initialized");
		return -ENODEV;
	}
	
	k_work_submit(&ppp_stop_work);
	return 0;
}

/* Check if PPP is active */
bool ppp_module_is_active(void)
{
	bool active;
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	active = ppp_active;
	k_mutex_unlock(&ppp_mutex);
	
	return active;
}

/* Check if PPP is connected */
bool ppp_module_is_connected(void)
{
	bool connected;
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	connected = ppp_connected;
	k_mutex_unlock(&ppp_mutex);
	
	return connected;
}

/* Get PPP module state */
enum ppp_module_state ppp_module_get_state(void)
{
	enum ppp_module_state state;
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	state = ppp_state;
	k_mutex_unlock(&ppp_mutex);
	
	return state;
}

/* Get PPP configuration */
int ppp_module_get_config(struct ppp_module_config *config)
{
	if (!config) {
		return -EINVAL;
	}
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	*config = ppp_config;
	k_mutex_unlock(&ppp_mutex);
	
	return 0;
}

/* Set PPP configuration */
int ppp_module_set_config(const struct ppp_module_config *config)
{
	if (!config) {
		return -EINVAL;
	}
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	ppp_config = *config;
	k_mutex_unlock(&ppp_mutex);
	
	LOG_INF("PPP configuration updated");
	return 0;
}

/* Get PPP statistics */
int ppp_module_get_stats(uint64_t *tx_bytes, uint64_t *rx_bytes,
			 uint64_t *tx_packets, uint64_t *rx_packets)
{
	if (!tx_bytes || !rx_bytes || !tx_packets || !rx_packets) {
		return -EINVAL;
	}
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	*tx_bytes = ppp_stats.tx_bytes;
	*rx_bytes = ppp_stats.rx_bytes;
	*tx_packets = ppp_stats.tx_packets;
	*rx_packets = ppp_stats.rx_packets;
	k_mutex_unlock(&ppp_mutex);
	
	return 0;
}

/* Reset PPP statistics */
int ppp_module_reset_stats(void)
{
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	memset(&ppp_stats, 0, sizeof(ppp_stats));
	k_mutex_unlock(&ppp_mutex);
	
	LOG_INF("PPP statistics reset");
	return 0;
}

/* Handle PPP data from UART */
int ppp_module_handle_uart_data(const uint8_t *data, size_t len)
{
	if (!data || len == 0) {
		return -EINVAL;
	}
	
	if (!ppp_active) {
		return -ENODEV;
	}
	
	/* TODO: Process PPP data from UART */
	LOG_DBG("Received %zu bytes from UART for PPP", len);
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	ppp_stats.rx_bytes += len;
	ppp_stats.rx_packets++;
	k_mutex_unlock(&ppp_mutex);
	
	return 0;
}

/* Send PPP data to UART */
int ppp_module_send_uart_data(const uint8_t *data, size_t len)
{
	if (!data || len == 0) {
		return -EINVAL;
	}
	
	if (!ppp_active) {
		return -ENODEV;
	}
	
	/* TODO: Send PPP data to UART */
	LOG_DBG("Sending %zu bytes to UART from PPP", len);
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	ppp_stats.tx_bytes += len;
	ppp_stats.tx_packets++;
	k_mutex_unlock(&ppp_mutex);
	
	return at_host_send(data, len);
}

/* Enable PPP forwarding of CGEV notifications */
void ppp_module_set_cgev_forwarding(bool enable)
{
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	cgev_forwarding = enable;
	k_mutex_unlock(&ppp_mutex);
	
	LOG_INF("PPP CGEV forwarding %s", enable ? "enabled" : "disabled");
}

/* Check if PPP forwarding of CGEV notifications is enabled */
bool ppp_module_get_cgev_forwarding(void)
{
	bool enabled;
	
	k_mutex_lock(&ppp_mutex, K_FOREVER);
	enabled = cgev_forwarding;
	k_mutex_unlock(&ppp_mutex);
	
	return enabled;
}
