/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef WIFI_MODULE_H_
#define WIFI_MODULE_H_

#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize the WiFi module
 * 
 * @return 0 on success, negative error code on failure
 */
int wifi_module_init(void);

/**
 * @brief Start the WiFi module (enable SoftAP)
 * 
 * @return 0 on success, negative error code on failure
 */
int wifi_module_start(void);

/**
 * @brief Stop the WiFi module (disable SoftAP)
 * 
 * @return 0 on success, negative error code on failure
 */
int wifi_module_stop(void);

/**
 * @brief Get WiFi network interface
 * 
 * @return Pointer to network interface or NULL if not available
 */
struct net_if *wifi_module_get_iface(void);

/**
 * @brief Check if WiFi AP is enabled
 * 
 * @return true if AP is enabled, false otherwise
 */
bool wifi_module_is_ap_enabled(void);

/**
 * @brief Get number of connected clients
 * 
 * @return Number of connected clients
 */
int wifi_module_get_client_count(void);

/**
 * @brief Get WiFi AP SSID
 * 
 * @param buf Buffer to store SSID
 * @param buf_size Size of the buffer
 * @return 0 on success, negative error code on failure
 */
int wifi_module_get_ssid(char *buf, size_t buf_size);

#ifdef __cplusplus
}
#endif

#endif /* WIFI_MODULE_H_ */
