/*
 * Cellular WiFi Router - WiFi Module Header
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef WIFI_MODULE_H
#define WIFI_MODULE_H

#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/wifi_mgmt.h>
#include "../common/events.h"

#ifdef __cplusplus
extern "C" {
#endif

/* WiFi Module States */
enum wifi_state {
	WIFI_STATE_DISABLED,
	WIFI_STATE_INITIALIZING,
	WIFI_STATE_STARTING_AP,
	WIFI_STATE_AP_ACTIVE,
	WIFI_STATE_AP_ERROR,
	WIFI_STATE_POWER_SAVING,
};

/* WiFi AP Configuration */
struct wifi_ap_config {
	char ssid[32];
	char password[64];
	uint8_t channel;
	enum wifi_security_type security;
	uint8_t max_clients;
	bool hidden;
	uint16_t beacon_interval;
	uint8_t dtim_period;
};

/* DHCP Server Configuration */
struct dhcp_server_config {
	char pool_start[16];
	char pool_end[16];
	char gateway[16];
	char netmask[16];
	char dns_primary[16];
	char dns_secondary[16];
	uint32_t lease_time_sec;
};

/* WiFi Client Information */
struct wifi_client_info {
	uint8_t mac[6];
	char ip_addr[16];
	int64_t connect_time;
	uint32_t rx_bytes;
	uint32_t tx_bytes;
	int8_t rssi;
	bool active;
};

/* WiFi Statistics */
struct wifi_stats {
	enum wifi_state current_state;
	uint8_t connected_clients;
	uint32_t total_connections;
	uint32_t total_disconnections;
	uint32_t ap_uptime_sec;
	uint32_t data_rx_bytes;
	uint32_t data_tx_bytes;
	uint32_t packets_forwarded;
	uint32_t packets_dropped;
	int8_t tx_power_dbm;
	uint8_t current_channel;
};

/* WiFi Module Functions */
int wifi_module_init(void);
int wifi_module_shutdown(void);
bool wifi_module_is_healthy(void);

/* Access Point Management */
int wifi_start_ap(void);
int wifi_stop_ap(void);
bool wifi_is_ap_active(void);
enum wifi_state wifi_get_state(void);

/* Configuration Management */
int wifi_set_ap_config(const struct wifi_ap_config *config);
int wifi_get_ap_config(struct wifi_ap_config *config);
int wifi_set_dhcp_config(const struct dhcp_server_config *config);
int wifi_get_dhcp_config(struct dhcp_server_config *config);
int wifi_load_config(void);
int wifi_save_config(void);

/* Client Management */
int wifi_get_connected_clients(struct wifi_client_info *clients, size_t max_clients);
int wifi_get_client_count(void);
int wifi_disconnect_client(const uint8_t *mac);
int wifi_get_client_info(const uint8_t *mac, struct wifi_client_info *info);

/* Statistics and Monitoring */
int wifi_get_stats(struct wifi_stats *stats);
int wifi_reset_stats(void);

/* Power Management */
int wifi_enter_power_saving(void);
int wifi_exit_power_saving(void);
int wifi_set_tx_power(int8_t power_dbm);
int wifi_get_tx_power(int8_t *power_dbm);

/* Network Interface Management */
struct net_if *wifi_get_interface(void);
int wifi_get_ap_ip_address(struct in_addr *addr);
int wifi_get_dhcp_pool_info(struct in_addr *start, struct in_addr *end);

/* DHCP Server Management */
int wifi_start_dhcp_server(void);
int wifi_stop_dhcp_server(void);
bool wifi_is_dhcp_server_active(void);
int wifi_get_dhcp_leases(struct wifi_client_info *leases, size_t max_leases);

/* Scanning and Channel Management */
int wifi_scan_channels(struct wifi_scan_result *results, size_t max_results);
int wifi_set_channel(uint8_t channel);
int wifi_get_channel(uint8_t *channel);
int wifi_get_best_channel(uint8_t *channel);

/* Security Management */
int wifi_set_security(enum wifi_security_type security, const char *password);
int wifi_get_security(enum wifi_security_type *security);

/* Callback Registration */
typedef void (*wifi_event_callback_t)(enum wifi_event_type type, void *data);
int wifi_register_event_callback(wifi_event_callback_t callback);

#ifdef __cplusplus
}
#endif

#endif /* WIFI_MODULE_H */
