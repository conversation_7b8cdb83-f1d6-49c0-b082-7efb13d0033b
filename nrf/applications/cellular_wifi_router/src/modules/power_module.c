/*
 * Cellular WiFi Router - Power Module Implementation
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include "power_module.h"
#include "../common/events.h"
#include "../common/utils.h"
#include <zephyr/logging/log.h>
#include <zephyr/zbus/zbus.h>
#include <zephyr/smf.h>
#include <zephyr/drivers/gpio.h>
#include <zephyr/drivers/adc.h>
#include <zephyr/pm/pm.h>
#include <zephyr/pm/device.h>

LOG_MODULE_REGISTER(power_module, CONFIG_LOG_DEFAULT_LEVEL);

/* State Machine Context */
static struct smf_ctx power_smf_ctx;
static enum power_state current_state = POWER_STATE_DISABLED;
static enum power_mode current_mode = POWER_MODE_BALANCED;
static bool power_healthy = false;

/* Power Configuration */
static struct power_config config = {
	.low_battery_threshold_mv = 3300,
	.critical_battery_threshold_mv = 3000,
	.cellular_idle_timeout_sec = 300,
	.wifi_idle_timeout_sec = 600,
	.system_idle_timeout_sec = 1800,
	.auto_power_save = true,
	.ui_disable_on_low_power = true,
	.default_mode = POWER_MODE_BALANCED,
	.led_brightness_normal = 50,
	.led_brightness_low_power = 10,
};

/* Battery Information */
static struct battery_info battery = {0};

/* Power Statistics */
static struct power_stats stats = {0};

/* Work items */
static struct k_work_delayable battery_monitor_work;
static struct k_work_delayable power_save_work;
static struct k_work_delayable stats_work;

/* Event callback */
static power_event_callback_t event_callback = NULL;

/* ZBUS Observers */
ZBUS_SUBSCRIBER_DEFINE(power_system_events_sub, 4);

/* Forward declarations */
static void battery_monitor_work_handler(struct k_work *work);
static void power_save_work_handler(struct k_work *work);
static void stats_work_handler(struct k_work *work);
static int power_read_battery_voltage(void);
static int power_calculate_battery_percentage(uint16_t voltage_mv);
static void power_check_battery_thresholds(void);
static void power_publish_event(enum power_event_type type);

/* Work handlers */
static void battery_monitor_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	/* Read battery voltage */
	int ret = power_read_battery_voltage();
	if (ret == 0) {
		/* Calculate battery percentage */
		battery.percentage = power_calculate_battery_percentage(battery.voltage_mv);
		
		/* Check thresholds */
		power_check_battery_thresholds();
		
		/* Publish battery update event */
		power_publish_event(POWER_EVENT_BATTERY_UPDATE);
	}
	
	/* Check USB connection status */
	bool usb_connected = false; /* TODO: Implement USB detection */
	if (usb_connected != battery.usb_connected) {
		battery.usb_connected = usb_connected;
		LOG_INF("USB %s", usb_connected ? "connected" : "disconnected");
	}
	
	/* Check charging status */
	bool charging = false; /* TODO: Implement charging detection */
	if (charging != battery.charging) {
		battery.charging = charging;
		if (charging) {
			current_state = POWER_STATE_CHARGING;
			power_publish_event(POWER_EVENT_CHARGING_STARTED);
		} else {
			current_state = POWER_STATE_NORMAL;
			power_publish_event(POWER_EVENT_CHARGING_STOPPED);
		}
	}
	
	/* Reschedule battery monitoring */
	k_work_reschedule(&battery_monitor_work, K_SECONDS(30));
}

static void power_save_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	if (config.auto_power_save && current_state == POWER_STATE_NORMAL) {
		LOG_INF("Entering automatic power save mode");
		power_enter_low_power_mode();
	}
}

static void stats_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	/* Update statistics */
	stats.current_state = current_state;
	stats.current_mode = current_mode;
	stats.uptime_sec++;
	
	if (current_state == POWER_STATE_LOW_POWER) {
		stats.time_in_low_power_sec++;
	}
	
	if (battery.charging) {
		stats.time_charging_sec++;
	}
	
	/* Update voltage statistics */
	if (battery.voltage_mv > 0) {
		if (stats.min_voltage_mv == 0 || battery.voltage_mv < stats.min_voltage_mv) {
			stats.min_voltage_mv = battery.voltage_mv;
		}
		if (battery.voltage_mv > stats.max_voltage_mv) {
			stats.max_voltage_mv = battery.voltage_mv;
		}
		
		/* Simple moving average for average voltage */
		if (stats.avg_voltage_mv == 0) {
			stats.avg_voltage_mv = battery.voltage_mv;
		} else {
			stats.avg_voltage_mv = (stats.avg_voltage_mv * 9 + battery.voltage_mv) / 10;
		}
	}
	
	/* Reschedule stats work */
	k_work_reschedule(&stats_work, K_SECONDS(1));
}

static int power_read_battery_voltage(void)
{
	/* TODO: Implement actual battery voltage reading using ADC */
	/* For now, simulate a battery voltage */
	static uint16_t simulated_voltage = 3700;
	
	/* Simulate battery discharge */
	if (!battery.charging && simulated_voltage > 3000) {
		simulated_voltage -= 1; /* Slow discharge simulation */
	}
	
	/* Simulate charging */
	if (battery.charging && simulated_voltage < 4200) {
		simulated_voltage += 2; /* Faster charging simulation */
	}
	
	battery.voltage_mv = simulated_voltage;
	
	LOG_DBG("Battery voltage: %d mV", battery.voltage_mv);
	return 0;
}

static int power_calculate_battery_percentage(uint16_t voltage_mv)
{
	/* Simple linear calculation between 3.0V (0%) and 4.2V (100%) */
	if (voltage_mv <= 3000) {
		return 0;
	}
	if (voltage_mv >= 4200) {
		return 100;
	}
	
	return ((voltage_mv - 3000) * 100) / (4200 - 3000);
}

static void power_check_battery_thresholds(void)
{
	bool was_low = battery.low_battery;
	bool was_critical = battery.critical_battery;
	
	battery.low_battery = (battery.voltage_mv <= config.low_battery_threshold_mv);
	battery.critical_battery = (battery.voltage_mv <= config.critical_battery_threshold_mv);
	
	/* Check for low battery transition */
	if (battery.low_battery && !was_low) {
		LOG_WRN("Low battery detected: %d mV", battery.voltage_mv);
		current_state = POWER_STATE_LOW_POWER;
		stats.low_battery_events++;
		power_publish_event(POWER_EVENT_LOW_BATTERY);
	}
	
	/* Check for critical battery transition */
	if (battery.critical_battery && !was_critical) {
		LOG_ERR("Critical battery detected: %d mV", battery.voltage_mv);
		current_state = POWER_STATE_CRITICAL;
		stats.critical_battery_events++;
		power_publish_event(POWER_EVENT_CRITICAL_BATTERY);
	}
	
	/* Recovery from low/critical battery */
	if (!battery.low_battery && was_low) {
		LOG_INF("Battery recovered from low state");
		current_state = POWER_STATE_NORMAL;
	}
}

static void power_publish_event(enum power_event_type type)
{
	struct power_event event = {
		.type = type,
		.battery_voltage_mv = battery.voltage_mv,
		.battery_percentage = battery.percentage,
		.charging = battery.charging,
		.usb_connected = battery.usb_connected,
	};
	
	events_publish_power(&event);
	
	/* Call registered callback if available */
	if (event_callback) {
		event_callback(type, &event);
	}
}

int power_module_init(void)
{
	LOG_INF("Initializing power module");
	
	/* Initialize battery info */
	memset(&battery, 0, sizeof(battery));
	
	/* Initialize work items */
	k_work_init_delayable(&battery_monitor_work, battery_monitor_work_handler);
	k_work_init_delayable(&power_save_work, power_save_work_handler);
	k_work_init_delayable(&stats_work, stats_work_handler);
	
	/* Set initial state */
	current_state = POWER_STATE_INITIALIZING;
	current_mode = config.default_mode;
	
	/* Start battery monitoring */
	k_work_reschedule(&battery_monitor_work, K_SECONDS(1));
	
	/* Start statistics updates */
	k_work_reschedule(&stats_work, K_SECONDS(5));
	
	/* Schedule power save check */
	if (config.auto_power_save) {
		k_work_reschedule(&power_save_work, K_SECONDS(config.system_idle_timeout_sec));
	}
	
	current_state = POWER_STATE_NORMAL;
	power_healthy = true;
	
	LOG_INF("Power module initialized successfully");
	return 0;
}

int power_set_mode(enum power_mode mode)
{
	if (mode == current_mode) {
		return 0;
	}
	
	LOG_INF("Setting power mode: %d", mode);
	current_mode = mode;
	
	/* Apply power mode settings */
	switch (mode) {
	case POWER_MODE_PERFORMANCE:
		/* Maximum performance settings */
		break;
		
	case POWER_MODE_BALANCED:
		/* Balanced settings */
		break;
		
	case POWER_MODE_POWER_SAVE:
		/* Power saving settings */
		break;
		
	case POWER_MODE_ULTRA_LOW_POWER:
		/* Ultra low power settings */
		break;
	}
	
	return 0;
}

enum power_mode power_get_mode(void)
{
	return current_mode;
}

enum power_state power_get_state(void)
{
	return current_state;
}

int power_get_battery_info(struct battery_info *info)
{
	if (!info) {
		return -EINVAL;
	}
	
	*info = battery;
	return 0;
}

int power_get_battery_voltage(uint16_t *voltage_mv)
{
	if (!voltage_mv) {
		return -EINVAL;
	}
	
	*voltage_mv = battery.voltage_mv;
	return 0;
}

int power_get_battery_percentage(uint8_t *percentage)
{
	if (!percentage) {
		return -EINVAL;
	}
	
	*percentage = battery.percentage;
	return 0;
}

bool power_is_charging(void)
{
	return battery.charging;
}

bool power_is_usb_connected(void)
{
	return battery.usb_connected;
}

bool power_is_low_battery(void)
{
	return battery.low_battery;
}

bool power_is_critical_battery(void)
{
	return battery.critical_battery;
}

int power_enter_low_power_mode(void)
{
	LOG_INF("Entering low power mode");
	
	current_state = POWER_STATE_LOW_POWER;
	current_mode = POWER_MODE_POWER_SAVE;
	
	/* TODO: Implement actual power saving measures */
	/* - Reduce CPU frequency */
	/* - Disable unnecessary peripherals */
	/* - Reduce LED brightness */
	/* - Increase sleep intervals */
	
	power_publish_event(POWER_EVENT_SLEEP_REQUEST);
	
	return 0;
}

int power_exit_low_power_mode(void)
{
	LOG_INF("Exiting low power mode");
	
	current_state = POWER_STATE_NORMAL;
	current_mode = config.default_mode;
	
	/* TODO: Restore normal power settings */
	
	power_publish_event(POWER_EVENT_WAKE_REQUEST);
	
	return 0;
}

bool power_module_is_healthy(void)
{
	return power_healthy;
}

int power_module_shutdown(void)
{
	LOG_INF("Shutting down power module");
	
	/* Cancel work items */
	k_work_cancel_delayable(&battery_monitor_work);
	k_work_cancel_delayable(&power_save_work);
	k_work_cancel_delayable(&stats_work);
	
	power_healthy = false;
	current_state = POWER_STATE_DISABLED;
	
	LOG_INF("Power module shutdown completed");
	return 0;
}
