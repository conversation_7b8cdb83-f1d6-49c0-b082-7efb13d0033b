/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/pm/pm.h>
#include <zephyr/pm/device.h>
#include <zephyr/drivers/sensor.h>
#include <zephyr/zbus/zbus.h>

#include "power_module.h"
#include "ui_module.h"
#include "../events/router_events.h"

LOG_MODULE_REGISTER(power_module, CONFIG_LOG_DEFAULT_LEVEL);

/* Power management context */
struct power_ctx {
	struct k_work_delayable battery_work;
	struct k_work_delayable power_save_work;
	int battery_level;
	bool charging;
	bool power_save_active;
	uint32_t last_activity_time;
	uint32_t power_save_timeout;
};

static struct power_ctx power_ctx;

/* Battery monitoring work handler */
static void battery_work_handler(struct k_work *work)
{
	int previous_level = power_ctx.battery_level;
	bool previous_charging = power_ctx.charging;
	
	/* TODO: Read actual battery level from PMIC/ADC */
	/* For now, simulate battery level */
	static int simulated_level = 100;
	
	if (!power_ctx.charging) {
		/* Discharge simulation */
		if (simulated_level > 0) {
			simulated_level -= 1; /* 1% per minute in active mode */
		}
	} else {
		/* Charge simulation */
		if (simulated_level < 100) {
			simulated_level += 2; /* 2% per minute when charging */
		}
	}
	
	power_ctx.battery_level = simulated_level;
	
	/* Check for battery level changes */
	if (power_ctx.battery_level != previous_level) {
		LOG_DBG("Battery level: %d%%", power_ctx.battery_level);
		
		struct power_event evt = {
			.battery_level_percent = power_ctx.battery_level,
			.charging = power_ctx.charging,
			.power_save_active = power_ctx.power_save_active
		};
		
		if (power_ctx.battery_level <= 10 && previous_level > 10) {
			evt.type = POWER_EVENT_BATTERY_CRITICAL;
			LOG_WRN("Battery critical: %d%%", power_ctx.battery_level);
		} else if (power_ctx.battery_level <= 20 && previous_level > 20) {
			evt.type = POWER_EVENT_BATTERY_LOW;
			LOG_WRN("Battery low: %d%%", power_ctx.battery_level);
		}
		
		if (evt.type != 0) {
			zbus_chan_pub(&power_chan, &evt, K_MSEC(100));
		}
	}
	
	/* Check for charging state changes */
	if (power_ctx.charging != previous_charging) {
		struct power_event evt = {
			.battery_level_percent = power_ctx.battery_level,
			.charging = power_ctx.charging,
			.power_save_active = power_ctx.power_save_active
		};
		
		if (power_ctx.charging) {
			evt.type = POWER_EVENT_CHARGING_STARTED;
			LOG_INF("Charging started");
		} else {
			evt.type = POWER_EVENT_CHARGING_STOPPED;
			LOG_INF("Charging stopped");
		}
		
		zbus_chan_pub(&power_chan, &evt, K_MSEC(100));
	}
	
	/* Schedule next battery check */
	k_work_schedule(&power_ctx.battery_work, K_SECONDS(60));
}

/* Power save timeout work handler */
static void power_save_work_handler(struct k_work *work)
{
	uint32_t current_time = k_uptime_get_32();
	uint32_t idle_time = current_time - power_ctx.last_activity_time;
	
	if (power_ctx.power_save_timeout > 0 && 
	    idle_time >= power_ctx.power_save_timeout * 1000 &&
	    !power_ctx.power_save_active) {
		
		LOG_INF("Entering power save mode after %u seconds of inactivity", 
			idle_time / 1000);
		power_module_enter_power_save();
	}
	
	/* Schedule next check */
	k_work_schedule(&power_ctx.power_save_work, K_SECONDS(30));
}

/* Activity tracking - call this when there's user activity */
static void update_activity_time(void)
{
	power_ctx.last_activity_time = k_uptime_get_32();
	
	/* Exit power save if active */
	if (power_ctx.power_save_active) {
		power_module_exit_power_save();
	}
}

/* ZBUS observers for activity tracking */
static void ui_event_observer(const struct zbus_channel *chan)
{
	const struct ui_event *evt = zbus_chan_const_msg(chan);
	
	switch (evt->type) {
	case UI_EVENT_BUTTON_PRESSED:
	case UI_EVENT_LONG_PRESS:
		update_activity_time();
		break;
	default:
		break;
	}
}

static void wifi_event_observer(const struct zbus_channel *chan)
{
	const struct wifi_event *evt = zbus_chan_const_msg(chan);
	
	switch (evt->type) {
	case WIFI_EVENT_CLIENT_CONNECTED:
	case WIFI_EVENT_CLIENT_DISCONNECTED:
		update_activity_time();
		break;
	default:
		break;
	}
}

static void bridge_event_observer(const struct zbus_channel *chan)
{
	const struct bridge_event *evt = zbus_chan_const_msg(chan);
	
	switch (evt->type) {
	case BRIDGE_EVENT_PACKET_FORWARDED:
		update_activity_time();
		break;
	default:
		break;
	}
}

ZBUS_LISTENER_DEFINE(power_ui_listener, ui_event_observer);
ZBUS_LISTENER_DEFINE(power_wifi_listener, wifi_event_observer);
ZBUS_LISTENER_DEFINE(power_bridge_listener, bridge_event_observer);

/* Public API implementations */
int power_module_init(void)
{
	LOG_INF("Initializing power module");
	
	/* Initialize work items */
	k_work_init_delayable(&power_ctx.battery_work, battery_work_handler);
	k_work_init_delayable(&power_ctx.power_save_work, power_save_work_handler);
	
	/* Initialize state */
	power_ctx.battery_level = 100; /* Start with full battery */
	power_ctx.charging = false;
	power_ctx.power_save_active = false;
	power_ctx.last_activity_time = k_uptime_get_32();
	power_ctx.power_save_timeout = CONFIG_CELLULAR_WIFI_ROUTER_POWER_SAVE_TIMEOUT;
	
	/* Start battery monitoring */
	k_work_schedule(&power_ctx.battery_work, K_SECONDS(10));
	
	/* Start power save monitoring if timeout is configured */
	if (power_ctx.power_save_timeout > 0) {
		k_work_schedule(&power_ctx.power_save_work, K_SECONDS(30));
	}
	
	LOG_INF("Power module initialized");
	return 0;
}

int power_module_get_battery_level(void)
{
	return power_ctx.battery_level;
}

bool power_module_is_charging(void)
{
	return power_ctx.charging;
}

int power_module_enter_power_save(void)
{
	if (power_ctx.power_save_active) {
		return 0; /* Already in power save */
	}
	
	LOG_INF("Entering power save mode");
	
	power_ctx.power_save_active = true;
	
	/* Disable UI to save power */
#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED
	ui_module_enable(false);
#endif
	
	/* TODO: Implement additional power saving measures:
	 * - Reduce cellular activity
	 * - Lower WiFi beacon interval
	 * - Reduce CPU frequency
	 * - Disable unnecessary peripherals
	 */
	
	struct power_event evt = {
		.type = POWER_EVENT_POWER_SAVE_ENTERED,
		.battery_level_percent = power_ctx.battery_level,
		.charging = power_ctx.charging,
		.power_save_active = true
	};
	zbus_chan_pub(&power_chan, &evt, K_MSEC(100));
	
	return 0;
}

int power_module_exit_power_save(void)
{
	if (!power_ctx.power_save_active) {
		return 0; /* Not in power save */
	}
	
	LOG_INF("Exiting power save mode");
	
	power_ctx.power_save_active = false;
	
	/* Re-enable UI */
#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED
	ui_module_enable(true);
#endif
	
	/* TODO: Restore normal power settings */
	
	struct power_event evt = {
		.type = POWER_EVENT_POWER_SAVE_EXITED,
		.battery_level_percent = power_ctx.battery_level,
		.charging = power_ctx.charging,
		.power_save_active = false
	};
	zbus_chan_pub(&power_chan, &evt, K_MSEC(100));
	
	return 0;
}

bool power_module_is_power_save_active(void)
{
	return power_ctx.power_save_active;
}
