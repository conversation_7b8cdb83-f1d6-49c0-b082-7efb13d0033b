/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include "at_commands.h"
#include "at_host_module.h"
#include "cellular_module.h"
#include "wifi_module.h"
#include "bridge_module.h"
#include "power_module.h"
#include "ui_module.h"
#include "../common/events.h"
#include "../common/utils.h"
#include <zephyr/logging/log.h>
#include <zephyr/kernel.h>
#include <zephyr/sys/reboot.h>
#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include <stdarg.h>

LOG_MODULE_REGISTER(at_commands, CONFIG_LOG_DEFAULT_LEVEL);

/* Router state */
static bool router_active = false;
static bool ppp_mode_active = false;

/* AT command registration using the macro */
AT_HOST_CMD_CUSTOM(xrouter, "#XROUTER", handle_at_xrouter);
AT_HOST_CMD_CUSTOM(xwifi, "#XWIFI", handle_at_xwifi);
AT_HOST_CMD_CUSTOM(xstatus, "#XSTATUS", handle_at_xstatus);
AT_HOST_CMD_CUSTOM(xconfig, "#XCONFIG", handle_at_xconfig);
AT_HOST_CMD_CUSTOM(xstats, "#XSTATS", handle_at_xstats);
AT_HOST_CMD_CUSTOM(xppp, "#XPPP", handle_at_xppp);
AT_HOST_CMD_CUSTOM(xpower, "#XPOWER", handle_at_xpower);
AT_HOST_CMD_CUSTOM(xled, "#XLED", handle_at_xled);
AT_HOST_CMD_CUSTOM(xversion, "#XVERSION", handle_at_xversion);
AT_HOST_CMD_CUSTOM(xlog, "#XLOG", handle_at_xlog);

/* Initialize AT command handlers */
int at_commands_init(void)
{
	LOG_INF("Initializing AT command handlers");
	
	/* AT command handlers are automatically registered via AT_HOST_CMD_CUSTOM macro */
	
	LOG_INF("AT command handlers initialized");
	return 0;
}

/* Uninitialize AT command handlers */
void at_commands_uninit(void)
{
	LOG_INF("AT command handlers uninitialized");
}

/* Utility functions */
void at_cmd_rsp_send(const char *fmt, ...)
{
	char buffer[512];
	va_list args;
	
	va_start(args, fmt);
	vsnprintf(buffer, sizeof(buffer), fmt, args);
	va_end(args);
	
	at_host_rsp_send("%s", buffer);
}

void at_cmd_rsp_ok(void)
{
	at_host_rsp_send_ok();
}

void at_cmd_rsp_error(void)
{
	at_host_rsp_send_error();
}

void at_cmd_rsp_cme_error(int error_code)
{
	at_cmd_rsp_send("\r\n+CME ERROR: %d\r\n", error_code);
}

void at_cmd_rsp_cms_error(int error_code)
{
	at_cmd_rsp_send("\r\n+CMS ERROR: %d\r\n", error_code);
}

/* #XROUTER command handler */
int handle_at_xrouter(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int enable;
	
	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count != 1) {
			at_cmd_rsp_cme_error(50); /* Incorrect parameters */
			return -EINVAL;
		}
		
		ret = at_parser_num_get(parser, 1, &enable);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}
		
		if (enable == 1) {
			/* Start router */
			if (!router_active) {
				LOG_INF("Starting router via AT command");
				/* TODO: Start router modules */
				router_active = true;
			}
			at_cmd_rsp_ok();
		} else if (enable == 0) {
			/* Stop router */
			if (router_active) {
				LOG_INF("Stopping router via AT command");
				/* TODO: Stop router modules */
				router_active = false;
			}
			at_cmd_rsp_ok();
		} else {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		break;
		
	case AT_PARSER_CMD_TYPE_READ:
		at_cmd_rsp_send("\r\n#XROUTER: %d\r\n", router_active ? 1 : 0);
		at_cmd_rsp_ok();
		break;
		
	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XROUTER: (0,1)\r\n");
		at_cmd_rsp_ok();
		break;
		
	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}
	
	return 0;
}

/* #XWIFI command handler */
int handle_at_xwifi(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int enable;
	char ssid[64];
	char password[64];
	size_t ssid_len, password_len;
	
	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count < 1) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		
		ret = at_parser_num_get(parser, 1, &enable);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}
		
		if (enable == 1) {
			/* Start WiFi AP */
			if (param_count >= 3) {
				ssid_len = sizeof(ssid);
				ret = at_parser_string_get(parser, 2, ssid, &ssid_len);
				if (ret) {
					at_cmd_rsp_cme_error(50);
					return ret;
				}
				
				password_len = sizeof(password);
				ret = at_parser_string_get(parser, 3, password, &password_len);
				if (ret) {
					at_cmd_rsp_cme_error(50);
					return ret;
				}
				
				LOG_INF("Starting WiFi AP: SSID=%s", ssid);
				/* TODO: Start WiFi AP with SSID and password */
			} else {
				LOG_INF("Starting WiFi AP with default settings");
				/* TODO: Start WiFi AP with default settings */
			}
			at_cmd_rsp_ok();
		} else if (enable == 0) {
			/* Stop WiFi AP */
			LOG_INF("Stopping WiFi AP via AT command");
			/* TODO: Stop WiFi AP */
			at_cmd_rsp_ok();
		} else {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		break;
		
	case AT_PARSER_CMD_TYPE_READ:
		/* TODO: Get actual WiFi status */
		at_cmd_rsp_send("\r\n#XWIFI: 0,\"DefaultSSID\"\r\n");
		at_cmd_rsp_ok();
		break;
		
	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XWIFI: (0,1),<ssid>,<password>\r\n");
		at_cmd_rsp_ok();
		break;
		
	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}
	
	return 0;
}

/* #XSTATUS command handler */
int handle_at_xstatus(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int status_type = 0;
	
	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count != 1) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		
		ret = at_parser_num_get(parser, 1, &status_type);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}
		
		/* Fall through to read case */
		
	case AT_PARSER_CMD_TYPE_READ:
		switch (status_type) {
		case 0: /* Overall status */
			at_cmd_rsp_send("\r\n#XSTATUS: \"Router\",%d\r\n", router_active ? 1 : 0);
			at_cmd_rsp_send("#XSTATUS: \"Cellular\",%d\r\n", utils_is_cellular_connected() ? 1 : 0);
			at_cmd_rsp_send("#XSTATUS: \"WiFi\",%d\r\n", utils_is_wifi_ap_active() ? 1 : 0);
			at_cmd_rsp_send("#XSTATUS: \"PPP\",%d\r\n", ppp_mode_active ? 1 : 0);
			break;
			
		case 1: /* Cellular status */
			at_cmd_rsp_send("\r\n#XSTATUS: \"Cellular\",%d,%d\r\n", 
					utils_is_cellular_connected() ? 1 : 0,
					utils_get_signal_strength());
			break;
			
		case 2: /* WiFi status */
			at_cmd_rsp_send("\r\n#XSTATUS: \"WiFi\",%d,0\r\n", 
					utils_is_wifi_ap_active() ? 1 : 0);
			break;
			
		case 3: /* Bridge status */
			at_cmd_rsp_send("\r\n#XSTATUS: \"Bridge\",%d,0,0\r\n", 
					router_active ? 1 : 0);
			break;
			
		default:
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		at_cmd_rsp_ok();
		break;
		
	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XSTATUS: (0,1,2,3)\r\n");
		at_cmd_rsp_ok();
		break;
		
	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}
	
	return 0;
}

/* #XCONFIG command handler */
int handle_at_xconfig(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int config_op;
	char key[64];
	char value[128];
	size_t key_len, value_len;
	
	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count < 1) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		
		ret = at_parser_num_get(parser, 1, &config_op);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}
		
		switch (config_op) {
		case 1: /* Set configuration */
			if (param_count != 3) {
				at_cmd_rsp_cme_error(50);
				return -EINVAL;
			}
			
			key_len = sizeof(key);
			ret = at_parser_string_get(parser, 2, key, &key_len);
			if (ret) {
				at_cmd_rsp_cme_error(50);
				return ret;
			}
			
			value_len = sizeof(value);
			ret = at_parser_string_get(parser, 3, value, &value_len);
			if (ret) {
				at_cmd_rsp_cme_error(50);
				return ret;
			}
			
			LOG_INF("Setting config: %s = %s", key, value);
			/* TODO: Implement configuration setting */
			at_cmd_rsp_ok();
			break;
			
		case 2: /* Get configuration */
			if (param_count != 2) {
				at_cmd_rsp_cme_error(50);
				return -EINVAL;
			}
			
			key_len = sizeof(key);
			ret = at_parser_string_get(parser, 2, key, &key_len);
			if (ret) {
				at_cmd_rsp_cme_error(50);
				return ret;
			}
			
			LOG_INF("Getting config: %s", key);
			/* TODO: Implement configuration getting */
			at_cmd_rsp_send("\r\n#XCONFIG: \"%s\",\"default_value\"\r\n", key);
			at_cmd_rsp_ok();
			break;
			
		case 3: /* List all configurations */
			at_cmd_rsp_send("\r\n#XCONFIG: \"wifi_ssid\",\"CellularRouter\"\r\n");
			at_cmd_rsp_send("#XCONFIG: \"wifi_password\",\"password123\"\r\n");
			at_cmd_rsp_send("#XCONFIG: \"auto_start\",\"1\"\r\n");
			at_cmd_rsp_ok();
			break;
			
		case 4: /* Reset to defaults */
			LOG_INF("Resetting configuration to defaults");
			/* TODO: Implement configuration reset */
			at_cmd_rsp_ok();
			break;
			
		default:
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		break;
		
	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XCONFIG: (1,2,3,4),<key>,<value>\r\n");
		at_cmd_rsp_ok();
		break;
		
	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}
	
	return 0;
}

/* #XSTATS command handler */
int handle_at_xstats(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int stats_type = 0;

	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count != 1) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}

		ret = at_parser_num_get(parser, 1, &stats_type);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}

		/* Fall through to read case */

	case AT_PARSER_CMD_TYPE_READ:
		switch (stats_type) {
		case 0: /* All statistics */
			at_cmd_rsp_send("\r\n#XSTATS: \"Uptime\",%u\r\n", utils_get_uptime_seconds());
			at_cmd_rsp_send("#XSTATS: \"FreeHeap\",%zu\r\n", utils_get_free_heap());
			at_cmd_rsp_send("#XSTATS: \"SignalStrength\",%d\r\n", utils_get_signal_strength());
			break;

		case 1: /* Network statistics */
			at_cmd_rsp_send("\r\n#XSTATS: \"TxBytes\",0\r\n");
			at_cmd_rsp_send("#XSTATS: \"RxBytes\",0\r\n");
			at_cmd_rsp_send("#XSTATS: \"TxPackets\",0\r\n");
			at_cmd_rsp_send("#XSTATS: \"RxPackets\",0\r\n");
			break;

		case 2: /* Data usage statistics */
			at_cmd_rsp_send("\r\n#XSTATS: \"CellularTx\",0\r\n");
			at_cmd_rsp_send("#XSTATS: \"CellularRx\",0\r\n");
			at_cmd_rsp_send("#XSTATS: \"WiFiTx\",0\r\n");
			at_cmd_rsp_send("#XSTATS: \"WiFiRx\",0\r\n");
			break;

		case 3: /* Reset statistics */
			LOG_INF("Resetting statistics");
			/* TODO: Implement statistics reset */
			at_cmd_rsp_ok();
			return 0;

		default:
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		at_cmd_rsp_ok();
		break;

	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XSTATS: (0,1,2,3)\r\n");
		at_cmd_rsp_ok();
		break;

	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}

	return 0;
}

/* #XPPP command handler */
int handle_at_xppp(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int enable;

	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count != 1) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}

		ret = at_parser_num_get(parser, 1, &enable);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}

		if (enable == 1) {
			/* Start PPP mode */
			if (!ppp_mode_active) {
				LOG_INF("Starting PPP mode via AT command");
				/* TODO: Start PPP mode */
				ppp_mode_active = true;
			}
			at_cmd_rsp_ok();
		} else if (enable == 0) {
			/* Stop PPP mode */
			if (ppp_mode_active) {
				LOG_INF("Stopping PPP mode via AT command");
				/* TODO: Stop PPP mode */
				ppp_mode_active = false;
			}
			at_cmd_rsp_ok();
		} else {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		break;

	case AT_PARSER_CMD_TYPE_READ:
		at_cmd_rsp_send("\r\n#XPPP: %d\r\n", ppp_mode_active ? 1 : 0);
		at_cmd_rsp_ok();
		break;

	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XPPP: (0,1)\r\n");
		at_cmd_rsp_ok();
		break;

	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}

	return 0;
}

/* #XPOWER command handler */
int handle_at_xpower(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int power_op;

	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count != 1) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}

		ret = at_parser_num_get(parser, 1, &power_op);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}

		switch (power_op) {
		case 0: /* Exit low power mode */
			LOG_INF("Exiting low power mode");
			/* TODO: Exit low power mode */
			at_cmd_rsp_ok();
			break;

		case 1: /* Enter low power mode */
			LOG_INF("Entering low power mode");
			/* TODO: Enter low power mode */
			at_cmd_rsp_ok();
			break;

		case 2: /* System reset */
			LOG_INF("System reset requested via AT command");
			at_cmd_rsp_ok();
			k_sleep(K_MSEC(100)); /* Allow response to be sent */
			sys_reboot(SYS_REBOOT_COLD);
			break;

		case 3: /* System shutdown */
			LOG_INF("System shutdown requested via AT command");
			at_cmd_rsp_ok();
			k_sleep(K_MSEC(100)); /* Allow response to be sent */
			/* TODO: Implement system shutdown */
			break;

		default:
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}
		break;

	case AT_PARSER_CMD_TYPE_READ:
		/* TODO: Get actual power status */
		at_cmd_rsp_send("\r\n#XPOWER: 0\r\n"); /* Normal power mode */
		at_cmd_rsp_ok();
		break;

	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XPOWER: (0,1,2,3)\r\n");
		at_cmd_rsp_ok();
		break;

	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}

	return 0;
}

/* #XLED command handler */
int handle_at_xled(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int led_id, pattern, brightness = 100;

	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count < 2) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}

		ret = at_parser_num_get(parser, 1, &led_id);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}

		ret = at_parser_num_get(parser, 2, &pattern);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}

		if (param_count >= 3) {
			ret = at_parser_num_get(parser, 3, &brightness);
			if (ret) {
				at_cmd_rsp_cme_error(50);
				return ret;
			}
		}

		LOG_INF("Setting LED %d to pattern %d, brightness %d", led_id, pattern, brightness);
		/* TODO: Implement LED control */
		at_cmd_rsp_ok();
		break;

	case AT_PARSER_CMD_TYPE_READ:
		/* TODO: Get actual LED status */
		at_cmd_rsp_send("\r\n#XLED: 0,0,100\r\n");
		at_cmd_rsp_send("#XLED: 1,0,100\r\n");
		at_cmd_rsp_ok();
		break;

	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XLED: <led_id>,<pattern>,<brightness>\r\n");
		at_cmd_rsp_ok();
		break;

	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}

	return 0;
}

/* #XVERSION command handler */
int handle_at_xversion(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int detail_level = 0;

	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count != 1) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}

		ret = at_parser_num_get(parser, 1, &detail_level);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}

		/* Fall through to read case */

	case AT_PARSER_CMD_TYPE_READ:
		if (detail_level == 0) {
			at_cmd_rsp_send("\r\n#XVERSION: \"CellularWiFiRouter\",\"1.0.0\"\r\n");
		} else {
			at_cmd_rsp_send("\r\n#XVERSION: \"CellularWiFiRouter\",\"1.0.0\"\r\n");
			at_cmd_rsp_send("#XVERSION: \"NCS\",\"3.0.2\"\r\n");
			at_cmd_rsp_send("#XVERSION: \"Zephyr\",\"4.0.99\"\r\n");
			at_cmd_rsp_send("#XVERSION: \"BuildDate\",\"%s %s\"\r\n", __DATE__, __TIME__);
		}
		at_cmd_rsp_ok();
		break;

	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XVERSION: (0,1)\r\n");
		at_cmd_rsp_ok();
		break;

	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}

	return 0;
}

/* #XLOG command handler */
int handle_at_xlog(enum at_parser_cmd_type cmd_type, struct at_parser *parser, uint32_t param_count)
{
	int ret;
	int log_level;

	switch (cmd_type) {
	case AT_PARSER_CMD_TYPE_SET:
		if (param_count != 1) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}

		ret = at_parser_num_get(parser, 1, &log_level);
		if (ret) {
			at_cmd_rsp_cme_error(50);
			return ret;
		}

		if (log_level < 0 || log_level > 4) {
			at_cmd_rsp_cme_error(50);
			return -EINVAL;
		}

		LOG_INF("Setting log level to %d", log_level);
		/* TODO: Implement log level setting */
		at_cmd_rsp_ok();
		break;

	case AT_PARSER_CMD_TYPE_READ:
		/* TODO: Get actual log level */
		at_cmd_rsp_send("\r\n#XLOG: 3\r\n"); /* INFO level */
		at_cmd_rsp_ok();
		break;

	case AT_PARSER_CMD_TYPE_TEST:
		at_cmd_rsp_send("\r\n#XLOG: (0,1,2,3,4)\r\n");
		at_cmd_rsp_ok();
		break;

	default:
		at_cmd_rsp_error();
		return -EINVAL;
	}

	return 0;
}
