/*
 * Cellular WiFi Router - Bridge Module Header
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef BRIDGE_MODULE_H
#define BRIDGE_MODULE_H

#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_pkt.h>
#include "../common/events.h"

#ifdef __cplusplus
extern "C" {
#endif

/* Bridge Module States */
enum bridge_state {
	BRIDGE_STATE_DISABLED,
	BRIDGE_STATE_INITIALIZING,
	BRIDGE_STATE_WAITING_INTERFACES,
	BRIDGE_STATE_ACTIVE,
	BRIDGE_STATE_ERROR,
};

/* NAT Entry Structure */
struct nat_entry {
	struct in_addr internal_ip;
	struct in_addr external_ip;
	uint16_t internal_port;
	uint16_t external_port;
	uint8_t protocol;
	int64_t last_activity;
	uint32_t timeout_sec;
	bool active;
};

/* Bridge Statistics */
struct bridge_stats {
	enum bridge_state current_state;
	uint32_t packets_cellular_to_wifi;
	uint32_t packets_wifi_to_cellular;
	uint32_t bytes_cellular_to_wifi;
	uint32_t bytes_wifi_to_cellular;
	uint32_t packets_dropped;
	uint32_t nat_entries_active;
	uint32_t nat_entries_total;
	uint32_t nat_timeouts;
	uint32_t routing_errors;
	uint32_t uptime_sec;
};

/* Bridge Configuration */
struct bridge_config {
	uint32_t nat_timeout_sec;
	uint16_t nat_table_size;
	bool enable_nat;
	bool enable_forwarding;
	bool enable_dhcp_relay;
	uint32_t mtu_cellular;
	uint32_t mtu_wifi;
	bool log_packets;
};

/* Bridge Module Functions */
int bridge_module_init(void);
int bridge_module_shutdown(void);
bool bridge_module_is_healthy(void);

/* Bridge Control */
int bridge_start(void);
int bridge_stop(void);
bool bridge_is_active(void);
enum bridge_state bridge_get_state(void);

/* Configuration Management */
int bridge_set_config(const struct bridge_config *config);
int bridge_get_config(struct bridge_config *config);
int bridge_load_config(void);
int bridge_save_config(void);

/* Statistics and Monitoring */
int bridge_get_stats(struct bridge_stats *stats);
int bridge_reset_stats(void);

/* NAT Management */
int bridge_add_nat_entry(const struct in_addr *internal_ip, uint16_t internal_port,
			 const struct in_addr *external_ip, uint16_t external_port,
			 uint8_t protocol);
int bridge_remove_nat_entry(const struct in_addr *internal_ip, uint16_t internal_port,
			    uint8_t protocol);
int bridge_get_nat_entries(struct nat_entry *entries, size_t max_entries);
int bridge_clear_nat_table(void);

/* Packet Processing */
int bridge_process_packet(struct net_pkt *pkt, struct net_if *iface);
int bridge_forward_packet(struct net_pkt *pkt, struct net_if *src_iface, 
			  struct net_if *dst_iface);

/* NAT Translation */
int bridge_nat_translate_outbound(struct net_pkt *pkt);
int bridge_nat_translate_inbound(struct net_pkt *pkt);

/* Routing */
int bridge_add_route(const struct in_addr *dest, const struct in_addr *gateway,
		     struct net_if *iface);
int bridge_remove_route(const struct in_addr *dest);
int bridge_get_route(const struct in_addr *dest, struct in_addr *gateway,
		     struct net_if **iface);

/* Interface Management */
int bridge_bind_interfaces(struct net_if *cellular_iface, struct net_if *wifi_iface);
int bridge_unbind_interfaces(void);
struct net_if *bridge_get_cellular_interface(void);
struct net_if *bridge_get_wifi_interface(void);

/* DHCP Relay (if enabled) */
int bridge_dhcp_relay_enable(bool enable);
bool bridge_dhcp_relay_is_enabled(void);

/* Firewall Rules (basic) */
struct firewall_rule {
	struct in_addr src_ip;
	struct in_addr dst_ip;
	uint16_t src_port;
	uint16_t dst_port;
	uint8_t protocol;
	bool allow;
	bool active;
};

int bridge_add_firewall_rule(const struct firewall_rule *rule);
int bridge_remove_firewall_rule(int rule_id);
int bridge_get_firewall_rules(struct firewall_rule *rules, size_t max_rules);
int bridge_clear_firewall_rules(void);

/* Quality of Service (basic) */
enum qos_priority {
	QOS_PRIORITY_LOW = 0,
	QOS_PRIORITY_NORMAL = 1,
	QOS_PRIORITY_HIGH = 2,
	QOS_PRIORITY_CRITICAL = 3,
};

int bridge_set_qos_priority(const struct in_addr *ip, enum qos_priority priority);
int bridge_get_qos_priority(const struct in_addr *ip, enum qos_priority *priority);

/* Callback Registration */
typedef void (*bridge_event_callback_t)(enum bridge_event_type type, void *data);
int bridge_register_event_callback(bridge_event_callback_t callback);

/* Debug and Diagnostics */
int bridge_dump_nat_table(void);
int bridge_dump_routing_table(void);
int bridge_test_connectivity(const struct in_addr *dest);

#ifdef __cplusplus
}
#endif

#endif /* BRIDGE_MODULE_H */
