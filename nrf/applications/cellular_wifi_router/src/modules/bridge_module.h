/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef BRIDGE_MODULE_H_
#define BRIDGE_MODULE_H_

#include <zephyr/kernel.h>
#include <zephyr/net/net_if.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize the bridge module
 * 
 * @return 0 on success, negative error code on failure
 */
int bridge_module_init(void);

/**
 * @brief Start the bridge module
 * 
 * @return 0 on success, negative error code on failure
 */
int bridge_module_start(void);

/**
 * @brief Stop the bridge module
 * 
 * @return 0 on success, negative error code on failure
 */
int bridge_module_stop(void);

/**
 * @brief Check if bridge is enabled
 * 
 * @return true if enabled, false otherwise
 */
bool bridge_module_is_enabled(void);

/**
 * @brief Get packet forwarding statistics
 * 
 * @param packets_forwarded Pointer to store packet count
 * @param bytes_forwarded Pointer to store byte count
 * @return 0 on success, negative error code on failure
 */
int bridge_module_get_stats(uint32_t *packets_forwarded, uint32_t *bytes_forwarded);

/**
 * @brief Get NAT table entry count
 * 
 * @return Number of active NAT entries
 */
int bridge_module_get_nat_entries(void);

#ifdef __cplusplus
}
#endif

#endif /* BRIDGE_MODULE_H_ */
