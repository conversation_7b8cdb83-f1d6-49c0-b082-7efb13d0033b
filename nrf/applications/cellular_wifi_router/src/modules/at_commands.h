/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#ifndef AT_COMMANDS_H_
#define AT_COMMANDS_H_

/** @file at_commands.h
 *
 * @brief AT command handlers for cellular WiFi router
 * @{
 */

#include <zephyr/types.h>
#include <modem/at_parser.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Initialize AT command handlers
 *
 * @retval 0 If the operation was successful.
 *           Otherwise, a (negative) error code is returned.
 */
int at_commands_init(void);

/**
 * @brief Uninitialize AT command handlers
 */
void at_commands_uninit(void);

/* Router-specific AT commands */

/* AT command handlers are declared as static in the implementation file
 * and registered using the AT_HOST_CMD_CUSTOM macro. No external declarations needed.
 */

/* Utility functions */

/**
 * @brief Send formatted AT response
 *
 * @param fmt Format string
 * @param ... Arguments
 */
void at_cmd_rsp_send(const char *fmt, ...);

/**
 * @brief Send OK response
 */
void at_cmd_rsp_ok(void);

/**
 * @brief Send ERROR response
 */
void at_cmd_rsp_error(void);

/**
 * @brief Send CME ERROR response
 *
 * @param error_code CME error code
 */
void at_cmd_rsp_cme_error(int error_code);

/**
 * @brief Send CMS ERROR response
 *
 * @param error_code CMS error code
 */
void at_cmd_rsp_cms_error(int error_code);

#ifdef __cplusplus
}
#endif

/** @} */

#endif /* AT_COMMANDS_H_ */
