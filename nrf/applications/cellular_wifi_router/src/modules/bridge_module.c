/*
 * Cellular WiFi Router - Bridge Module Implementation
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include "bridge_module.h"
#include "cellular_module.h"
#include "wifi_module.h"
#include "../common/events.h"
#include "../common/utils.h"
#include <zephyr/logging/log.h>
#include <zephyr/zbus/zbus.h>
#include <zephyr/smf.h>
#include <zephyr/net/net_mgmt.h>
#include <zephyr/net/net_pkt.h>
#include <zephyr/net/net_core.h>

LOG_MODULE_REGISTER(bridge_module, CONFIG_LOG_DEFAULT_LEVEL);

/* State Machine Context */
static struct smf_ctx bridge_smf_ctx;
static enum bridge_state current_state = BRIDGE_STATE_DISABLED;
static bool bridge_healthy = false;

/* Bridge Configuration */
static struct bridge_config config = {
	.nat_timeout_sec = 300,
	.nat_table_size = 64,
	.enable_nat = true,
	.enable_forwarding = true,
	.enable_dhcp_relay = false,
	.mtu_cellular = 1500,
	.mtu_wifi = 1500,
	.log_packets = false,
};

/* Bridge Statistics */
static struct bridge_stats stats = {0};

/* Network Interfaces */
static struct net_if *cellular_iface = NULL;
static struct net_if *wifi_iface = NULL;

/* NAT Table */
static struct nat_entry nat_table[64];
static uint16_t nat_table_size = 64;
static uint16_t nat_entries_count = 0;

/* Work items */
static struct k_work_delayable interface_check_work;
static struct k_work_delayable nat_cleanup_work;
static struct k_work_delayable stats_work;

/* Event callback */
static bridge_event_callback_t event_callback = NULL;

/* ZBUS Observers */
ZBUS_SUBSCRIBER_DEFINE(bridge_cellular_events_sub, 4);
ZBUS_SUBSCRIBER_DEFINE(bridge_wifi_events_sub, 4);

/* Forward declarations */
static void interface_check_work_handler(struct k_work *work);
static void nat_cleanup_work_handler(struct k_work *work);
static void stats_work_handler(struct k_work *work);
static int bridge_setup_interfaces(void);
static int bridge_create_nat_entry(const struct in_addr *internal_ip, uint16_t internal_port,
				   uint8_t protocol, struct nat_entry **entry);
static struct nat_entry *bridge_find_nat_entry(const struct in_addr *ip, uint16_t port,
					       uint8_t protocol, bool internal);
static void bridge_publish_event(enum bridge_event_type type, int error_code);

/* Work handlers */
static void interface_check_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	/* Check if both interfaces are available and ready */
	cellular_iface = cellular_get_interface();
	wifi_iface = wifi_get_interface();
	
	if (cellular_iface && wifi_iface) {
		if (current_state == BRIDGE_STATE_WAITING_INTERFACES) {
			LOG_INF("Both interfaces available - starting bridge");
			current_state = BRIDGE_STATE_ACTIVE;
			bridge_publish_event(BRIDGE_EVENT_STARTED, 0);
		}
	} else {
		if (current_state == BRIDGE_STATE_ACTIVE) {
			LOG_WRN("Interface lost - bridge inactive");
			current_state = BRIDGE_STATE_WAITING_INTERFACES;
		}
	}
	
	/* Reschedule interface check */
	k_work_reschedule(&interface_check_work, K_SECONDS(5));
}

static void nat_cleanup_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	int64_t now = k_uptime_get();
	int cleaned = 0;
	
	/* Clean up expired NAT entries */
	for (int i = 0; i < nat_table_size; i++) {
		if (nat_table[i].active) {
			int64_t age_sec = (now - nat_table[i].last_activity) / 1000;
			
			if (age_sec > nat_table[i].timeout_sec) {
				LOG_DBG("Cleaning up expired NAT entry: %d.%d.%d.%d:%d",
					nat_table[i].internal_ip.s_addr & 0xFF,
					(nat_table[i].internal_ip.s_addr >> 8) & 0xFF,
					(nat_table[i].internal_ip.s_addr >> 16) & 0xFF,
					(nat_table[i].internal_ip.s_addr >> 24) & 0xFF,
					nat_table[i].internal_port);
				
				nat_table[i].active = false;
				nat_entries_count--;
				stats.nat_timeouts++;
				cleaned++;
			}
		}
	}
	
	if (cleaned > 0) {
		LOG_DBG("Cleaned up %d expired NAT entries", cleaned);
	}
	
	stats.nat_entries_active = nat_entries_count;
	
	/* Reschedule NAT cleanup */
	k_work_reschedule(&nat_cleanup_work, K_SECONDS(60));
}

static void stats_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	/* Update statistics */
	stats.current_state = current_state;
	stats.nat_entries_active = nat_entries_count;
	
	if (current_state == BRIDGE_STATE_ACTIVE) {
		stats.uptime_sec++;
	}
	
	/* Reschedule stats work */
	k_work_reschedule(&stats_work, K_SECONDS(1));
}

static int bridge_setup_interfaces(void)
{
	/* Get network interfaces */
	cellular_iface = cellular_get_interface();
	wifi_iface = wifi_get_interface();
	
	if (!cellular_iface) {
		LOG_WRN("Cellular interface not available");
	}
	
	if (!wifi_iface) {
		LOG_WRN("WiFi interface not available");
	}
	
	if (cellular_iface && wifi_iface) {
		LOG_INF("Both interfaces available");
		return 0;
	}
	
	return -ENODEV;
}

static int bridge_create_nat_entry(const struct in_addr *internal_ip, uint16_t internal_port,
				   uint8_t protocol, struct nat_entry **entry)
{
	/* Find free NAT table entry */
	for (int i = 0; i < nat_table_size; i++) {
		if (!nat_table[i].active) {
			nat_table[i].internal_ip = *internal_ip;
			nat_table[i].internal_port = internal_port;
			nat_table[i].protocol = protocol;
			nat_table[i].last_activity = k_uptime_get();
			nat_table[i].timeout_sec = config.nat_timeout_sec;
			nat_table[i].active = true;
			
			/* For simplicity, use a basic port mapping */
			nat_table[i].external_port = 10000 + i;
			
			nat_entries_count++;
			stats.nat_entries_total++;
			
			*entry = &nat_table[i];
			
			LOG_DBG("Created NAT entry: %d.%d.%d.%d:%d -> :%d",
				internal_ip->s_addr & 0xFF,
				(internal_ip->s_addr >> 8) & 0xFF,
				(internal_ip->s_addr >> 16) & 0xFF,
				(internal_ip->s_addr >> 24) & 0xFF,
				internal_port, nat_table[i].external_port);
			
			bridge_publish_event(BRIDGE_EVENT_NAT_ENTRY_ADDED, 0);
			return 0;
		}
	}
	
	LOG_ERR("NAT table full");
	return -ENOMEM;
}

static struct nat_entry *bridge_find_nat_entry(const struct in_addr *ip, uint16_t port,
					       uint8_t protocol, bool internal)
{
	for (int i = 0; i < nat_table_size; i++) {
		if (nat_table[i].active && nat_table[i].protocol == protocol) {
			if (internal) {
				if (nat_table[i].internal_ip.s_addr == ip->s_addr &&
				    nat_table[i].internal_port == port) {
					return &nat_table[i];
				}
			} else {
				if (nat_table[i].external_port == port) {
					return &nat_table[i];
				}
			}
		}
	}
	
	return NULL;
}

static void bridge_publish_event(enum bridge_event_type type, int error_code)
{
	struct bridge_event event = {
		.type = type,
		.cellular_iface = cellular_iface,
		.wifi_iface = wifi_iface,
		.packets_forwarded = stats.packets_cellular_to_wifi + stats.packets_wifi_to_cellular,
		.bytes_forwarded = stats.bytes_cellular_to_wifi + stats.bytes_wifi_to_cellular,
		.error_code = error_code,
	};
	
	events_publish_bridge(&event);
	
	/* Call registered callback if available */
	if (event_callback) {
		event_callback(type, &event);
	}
}

int bridge_module_init(void)
{
	int ret;
	
	LOG_INF("Initializing bridge module");
	
	/* Initialize NAT table */
	memset(nat_table, 0, sizeof(nat_table));
	nat_entries_count = 0;
	
	/* Initialize work items */
	k_work_init_delayable(&interface_check_work, interface_check_work_handler);
	k_work_init_delayable(&nat_cleanup_work, nat_cleanup_work_handler);
	k_work_init_delayable(&stats_work, stats_work_handler);
	
	/* Setup interfaces */
	ret = bridge_setup_interfaces();
	if (ret) {
		LOG_WRN("Interfaces not ready - will wait for them");
		current_state = BRIDGE_STATE_WAITING_INTERFACES;
	} else {
		current_state = BRIDGE_STATE_ACTIVE;
		bridge_publish_event(BRIDGE_EVENT_STARTED, 0);
	}
	
	/* Start periodic tasks */
	k_work_reschedule(&interface_check_work, K_SECONDS(2));
	k_work_reschedule(&nat_cleanup_work, K_SECONDS(60));
	k_work_reschedule(&stats_work, K_SECONDS(5));
	
	bridge_healthy = true;
	LOG_INF("Bridge module initialized successfully");
	return 0;
}

int bridge_start(void)
{
	if (current_state == BRIDGE_STATE_ACTIVE) {
		return 0;
	}
	
	LOG_INF("Starting bridge");
	current_state = BRIDGE_STATE_INITIALIZING;
	bridge_publish_event(BRIDGE_EVENT_STARTING, 0);
	
	/* Trigger interface check */
	k_work_reschedule(&interface_check_work, K_NO_WAIT);
	
	return 0;
}

int bridge_stop(void)
{
	if (current_state == BRIDGE_STATE_DISABLED) {
		return 0;
	}
	
	LOG_INF("Stopping bridge");
	current_state = BRIDGE_STATE_DISABLED;
	bridge_publish_event(BRIDGE_EVENT_STOPPED, 0);
	
	return 0;
}

bool bridge_is_active(void)
{
	return (current_state == BRIDGE_STATE_ACTIVE);
}

enum bridge_state bridge_get_state(void)
{
	return current_state;
}

int bridge_get_stats(struct bridge_stats *stats_out)
{
	if (!stats_out) {
		return -EINVAL;
	}
	
	*stats_out = stats;
	return 0;
}

int bridge_clear_nat_table(void)
{
	LOG_INF("Clearing NAT table");
	
	memset(nat_table, 0, sizeof(nat_table));
	nat_entries_count = 0;
	stats.nat_entries_active = 0;
	
	return 0;
}

bool bridge_module_is_healthy(void)
{
	return bridge_healthy;
}

int bridge_module_shutdown(void)
{
	LOG_INF("Shutting down bridge module");
	
	/* Cancel work items */
	k_work_cancel_delayable(&interface_check_work);
	k_work_cancel_delayable(&nat_cleanup_work);
	k_work_cancel_delayable(&stats_work);
	
	/* Clear NAT table */
	bridge_clear_nat_table();
	
	bridge_healthy = false;
	current_state = BRIDGE_STATE_DISABLED;
	
	LOG_INF("Bridge module shutdown completed");
	return 0;
}
