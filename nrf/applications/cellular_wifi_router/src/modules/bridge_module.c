/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/net/net_if.h>
#include <zephyr/net/net_mgmt.h>
#include <zephyr/net/net_pkt.h>
#include <zephyr/net/ethernet.h>
#include <zephyr/smf.h>
#include <zephyr/zbus/zbus.h>

#include "bridge_module.h"
#include "cellular_module.h"
#include "wifi_module.h"
#include "../events/router_events.h"

LOG_MODULE_REGISTER(bridge_module, CONFIG_LOG_DEFAULT_LEVEL);

/* Bridge state machine states */
enum bridge_state {
	BRIDGE_STATE_IDLE,
	BRIDGE_STATE_STARTING,
	BRIDGE_STATE_ENABLED,
	BRIDGE_STATE_STOPPING,
	BRIDGE_STATE_ERROR,
};

/* NAT entry structure */
struct nat_entry {
	struct in_addr internal_ip;
	struct in_addr external_ip;
	uint16_t internal_port;
	uint16_t external_port;
	uint8_t protocol;
	uint32_t timeout;
	bool in_use;
};

/* Bridge state machine context */
struct bridge_sm_ctx {
	struct smf_ctx ctx;
	struct net_if *cellular_iface;
	struct net_if *wifi_iface;
	struct k_work_delayable start_work;
	struct k_work_delayable stats_work;
	struct nat_entry nat_table[32];
	uint32_t packets_forwarded;
	uint32_t bytes_forwarded;
	uint16_t nat_entries_count;
	bool enabled;
};

static struct bridge_sm_ctx bridge_ctx;

/* Forward declarations */
static void bridge_state_idle_entry(void *o);
static void bridge_state_idle_run(void *o);
static void bridge_state_starting_entry(void *o);
static void bridge_state_starting_run(void *o);
static void bridge_state_enabled_entry(void *o);
static void bridge_state_enabled_run(void *o);
static void bridge_state_stopping_entry(void *o);
static void bridge_state_stopping_run(void *o);
static void bridge_state_error_entry(void *o);
static void bridge_state_error_run(void *o);

/* State machine definition */
static const struct smf_state bridge_states[] = {
	[BRIDGE_STATE_IDLE] = SMF_CREATE_STATE(
		bridge_state_idle_entry,
		bridge_state_idle_run,
		NULL,
		NULL,
		NULL),
	[BRIDGE_STATE_STARTING] = SMF_CREATE_STATE(
		bridge_state_starting_entry,
		bridge_state_starting_run,
		NULL,
		NULL,
		NULL),
	[BRIDGE_STATE_ENABLED] = SMF_CREATE_STATE(
		bridge_state_enabled_entry,
		bridge_state_enabled_run,
		NULL,
		NULL,
		NULL),
	[BRIDGE_STATE_STOPPING] = SMF_CREATE_STATE(
		bridge_state_stopping_entry,
		bridge_state_stopping_run,
		NULL,
		NULL,
		NULL),
	[BRIDGE_STATE_ERROR] = SMF_CREATE_STATE(
		bridge_state_error_entry,
		bridge_state_error_run,
		NULL,
		NULL,
		NULL),
};

/* NAT helper functions */
static struct nat_entry *find_nat_entry(struct in_addr *internal_ip, uint16_t internal_port, uint8_t protocol)
{
	for (int i = 0; i < ARRAY_SIZE(bridge_ctx.nat_table); i++) {
		struct nat_entry *entry = &bridge_ctx.nat_table[i];
		if (entry->in_use &&
		    entry->internal_ip.s_addr == internal_ip->s_addr &&
		    entry->internal_port == internal_port &&
		    entry->protocol == protocol) {
			return entry;
		}
	}
	return NULL;
}

static struct nat_entry *create_nat_entry(struct in_addr *internal_ip, uint16_t internal_port, uint8_t protocol)
{
	/* Find free entry */
	for (int i = 0; i < ARRAY_SIZE(bridge_ctx.nat_table); i++) {
		struct nat_entry *entry = &bridge_ctx.nat_table[i];
		if (!entry->in_use) {
			entry->internal_ip = *internal_ip;
			entry->internal_port = internal_port;
			entry->protocol = protocol;
			entry->external_port = 10000 + i; /* Simple port allocation */
			entry->timeout = k_uptime_get_32() + 300000; /* 5 minutes */
			entry->in_use = true;
			bridge_ctx.nat_entries_count++;
			
			LOG_DBG("Created NAT entry: %d.%d.%d.%d:%d -> :%d",
				(internal_ip->s_addr >> 0) & 0xFF,
				(internal_ip->s_addr >> 8) & 0xFF,
				(internal_ip->s_addr >> 16) & 0xFF,
				(internal_ip->s_addr >> 24) & 0xFF,
				internal_port, entry->external_port);
			
			struct bridge_event evt = {
				.type = BRIDGE_EVENT_NAT_ENTRY_ADDED,
				.nat_entries_count = bridge_ctx.nat_entries_count
			};
			zbus_chan_pub(&bridge_chan, &evt, K_MSEC(100));
			
			return entry;
		}
	}
	return NULL;
}

static void cleanup_nat_entries(void)
{
	uint32_t current_time = k_uptime_get_32();
	
	for (int i = 0; i < ARRAY_SIZE(bridge_ctx.nat_table); i++) {
		struct nat_entry *entry = &bridge_ctx.nat_table[i];
		if (entry->in_use && current_time > entry->timeout) {
			LOG_DBG("NAT entry expired: %d.%d.%d.%d:%d",
				(entry->internal_ip.s_addr >> 0) & 0xFF,
				(entry->internal_ip.s_addr >> 8) & 0xFF,
				(entry->internal_ip.s_addr >> 16) & 0xFF,
				(entry->internal_ip.s_addr >> 24) & 0xFF,
				entry->internal_port);
			
			entry->in_use = false;
			bridge_ctx.nat_entries_count--;
			
			struct bridge_event evt = {
				.type = BRIDGE_EVENT_NAT_ENTRY_REMOVED,
				.nat_entries_count = bridge_ctx.nat_entries_count
			};
			zbus_chan_pub(&bridge_chan, &evt, K_MSEC(100));
		}
	}
}

/* Packet forwarding function (simplified NAT implementation) */
static enum net_verdict packet_forward(struct net_pkt *pkt)
{
	struct net_if *pkt_iface = net_pkt_iface(pkt);
	
	if (!bridge_ctx.enabled) {
		return NET_CONTINUE;
	}
	
	/* Simple packet forwarding logic */
	if (pkt_iface == bridge_ctx.wifi_iface) {
		/* WiFi to Cellular */
		LOG_DBG("Forwarding packet from WiFi to Cellular");
		net_pkt_set_iface(pkt, bridge_ctx.cellular_iface);
		bridge_ctx.packets_forwarded++;
		bridge_ctx.bytes_forwarded += net_pkt_get_len(pkt);
		
		struct bridge_event evt = {
			.type = BRIDGE_EVENT_PACKET_FORWARDED,
			.packets_forwarded = bridge_ctx.packets_forwarded,
			.bytes_forwarded = bridge_ctx.bytes_forwarded
		};
		zbus_chan_pub(&bridge_chan, &evt, K_MSEC(100));
		
		return NET_CONTINUE;
		
	} else if (pkt_iface == bridge_ctx.cellular_iface) {
		/* Cellular to WiFi */
		LOG_DBG("Forwarding packet from Cellular to WiFi");
		net_pkt_set_iface(pkt, bridge_ctx.wifi_iface);
		bridge_ctx.packets_forwarded++;
		bridge_ctx.bytes_forwarded += net_pkt_get_len(pkt);
		
		struct bridge_event evt = {
			.type = BRIDGE_EVENT_PACKET_FORWARDED,
			.packets_forwarded = bridge_ctx.packets_forwarded,
			.bytes_forwarded = bridge_ctx.bytes_forwarded
		};
		zbus_chan_pub(&bridge_chan, &evt, K_MSEC(100));
		
		return NET_CONTINUE;
	}
	
	return NET_CONTINUE;
}

/* Work handlers */
static void start_work_handler(struct k_work *work)
{
	smf_set_state(SMF_CTX(&bridge_ctx), &bridge_states[BRIDGE_STATE_STARTING]);
}

static void stats_work_handler(struct k_work *work)
{
	/* Cleanup expired NAT entries */
	cleanup_nat_entries();
	
	/* Log statistics */
	LOG_DBG("Bridge stats: %u packets, %u bytes, %u NAT entries",
		bridge_ctx.packets_forwarded, bridge_ctx.bytes_forwarded,
		bridge_ctx.nat_entries_count);
	
	/* Schedule next cleanup */
	k_work_schedule(&bridge_ctx.stats_work, K_SECONDS(60));
}

/* State machine implementations */
static void bridge_state_idle_entry(void *o)
{
	LOG_INF("Bridge: Entering IDLE state");
	bridge_ctx.enabled = false;
}

static void bridge_state_idle_run(void *o)
{
	/* Stay in idle until explicitly started */
}

static void bridge_state_starting_entry(void *o)
{
	LOG_INF("Bridge: Entering STARTING state");
	
	struct bridge_event evt = {
		.type = BRIDGE_EVENT_ENABLING
	};
	zbus_chan_pub(&bridge_chan, &evt, K_MSEC(100));
}

static void bridge_state_starting_run(void *o)
{
	/* Get network interfaces */
	bridge_ctx.cellular_iface = cellular_module_get_iface();
	bridge_ctx.wifi_iface = wifi_module_get_iface();
	
	if (!bridge_ctx.cellular_iface || !bridge_ctx.wifi_iface) {
		LOG_ERR("Failed to get network interfaces");
		smf_set_state(SMF_CTX(&bridge_ctx), &bridge_states[BRIDGE_STATE_ERROR]);
		return;
	}
	
	if (!cellular_module_is_connected() || !wifi_module_is_ap_enabled()) {
		LOG_WRN("Waiting for cellular and WiFi to be ready");
		/* Stay in starting state */
		return;
	}
	
	LOG_INF("Both interfaces ready, enabling bridge");
	smf_set_state(SMF_CTX(&bridge_ctx), &bridge_states[BRIDGE_STATE_ENABLED]);
}

static void bridge_state_enabled_entry(void *o)
{
	LOG_INF("Bridge: Entering ENABLED state");
	
	bridge_ctx.enabled = true;
	
	/* Start statistics monitoring */
	k_work_schedule(&bridge_ctx.stats_work, K_SECONDS(60));
	
	struct bridge_event evt = {
		.type = BRIDGE_EVENT_ENABLED
	};
	zbus_chan_pub(&bridge_chan, &evt, K_MSEC(100));
}

static void bridge_state_enabled_run(void *o)
{
	/* Monitor bridge status */
}

static void bridge_state_stopping_entry(void *o)
{
	LOG_INF("Bridge: Entering STOPPING state");
	
	bridge_ctx.enabled = false;
	
	/* Cancel work */
	k_work_cancel_delayable(&bridge_ctx.stats_work);
	
	/* Clear NAT table */
	memset(bridge_ctx.nat_table, 0, sizeof(bridge_ctx.nat_table));
	bridge_ctx.nat_entries_count = 0;
	
	smf_set_state(SMF_CTX(&bridge_ctx), &bridge_states[BRIDGE_STATE_IDLE]);
}

static void bridge_state_stopping_run(void *o)
{
	/* Transition handled in entry */
}

static void bridge_state_error_entry(void *o)
{
	LOG_ERR("Bridge: Entering ERROR state");
	
	struct bridge_event evt = {
		.type = BRIDGE_EVENT_ERROR,
		.error_code = -EIO
	};
	zbus_chan_pub(&bridge_chan, &evt, K_MSEC(100));
}

static void bridge_state_error_run(void *o)
{
	/* Stay in error state until reset */
}

/* Public API implementations */
int bridge_module_init(void)
{
	LOG_INF("Initializing bridge module");

	/* Initialize state machine */
	smf_set_initial(SMF_CTX(&bridge_ctx), &bridge_states[BRIDGE_STATE_IDLE]);

	/* Initialize work items */
	k_work_init_delayable(&bridge_ctx.start_work, start_work_handler);
	k_work_init_delayable(&bridge_ctx.stats_work, stats_work_handler);

	/* Initialize NAT table */
	memset(bridge_ctx.nat_table, 0, sizeof(bridge_ctx.nat_table));
	bridge_ctx.nat_entries_count = 0;
	bridge_ctx.packets_forwarded = 0;
	bridge_ctx.bytes_forwarded = 0;

	LOG_INF("Bridge module initialized");
	return 0;
}

int bridge_module_start(void)
{
	LOG_INF("Starting bridge module");

	/* Trigger bridge start */
	k_work_schedule(&bridge_ctx.start_work, K_NO_WAIT);

	return 0;
}

int bridge_module_stop(void)
{
	LOG_INF("Stopping bridge module");

	/* Cancel any pending work */
	k_work_cancel_delayable(&bridge_ctx.start_work);
	k_work_cancel_delayable(&bridge_ctx.stats_work);

	/* Transition to stopping state */
	smf_set_state(SMF_CTX(&bridge_ctx), &bridge_states[BRIDGE_STATE_STOPPING]);

	return 0;
}

bool bridge_module_is_enabled(void)
{
	return bridge_ctx.enabled;
}

int bridge_module_get_stats(uint32_t *packets_forwarded, uint32_t *bytes_forwarded)
{
	if (!packets_forwarded || !bytes_forwarded) {
		return -EINVAL;
	}

	*packets_forwarded = bridge_ctx.packets_forwarded;
	*bytes_forwarded = bridge_ctx.bytes_forwarded;

	return 0;
}

int bridge_module_get_nat_entries(void)
{
	return bridge_ctx.nat_entries_count;
}
