/*
 * Cellular WiFi Router - Main Application
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/kernel.h>
#include <zephyr/logging/log.h>
#include <zephyr/zbus/zbus.h>
#include <zephyr/sys/reboot.h>

#include "common/events.h"
#include "common/utils.h"
#include "modules/cellular_module.h"
#include "modules/wifi_module.h"
#include "modules/bridge_module.h"
#include "modules/ui_module.h"
#include "modules/power_module.h"

LOG_MODULE_REGISTER(main, CONFIG_LOG_DEFAULT_LEVEL);

/* Application version */
#define APP_VERSION_MAJOR 1
#define APP_VERSION_MINOR 0
#define APP_VERSION_PATCH 0

/* Main application state machine */
enum app_state {
	APP_STATE_INIT,
	APP_STATE_STARTING,
	APP_STATE_RUNNING,
	APP_STATE_ERROR,
	APP_STATE_SHUTDOWN,
};

static enum app_state current_state = APP_STATE_INIT;
static struct k_work_delayable watchdog_work;
static struct k_work_delayable stats_work;

/* System event observer */
ZBUS_SUBSCRIBER_DEFINE(system_events_sub, 4);

/* Forward declarations */
static void system_event_handler(const struct zbus_channel *chan);
static void watchdog_work_handler(struct k_work *work);
static void stats_work_handler(struct k_work *work);
static int app_init(void);
static int app_start_modules(void);
static void app_shutdown(void);

/* System event observer callback */
static void system_event_handler(const struct zbus_channel *chan)
{
	const struct system_event *event = zbus_chan_const_msg(chan);
	
	LOG_DBG("Received system event: type=%d, error=%d", 
		event->type, event->error_code);
	
	switch (event->type) {
	case SYSTEM_EVENT_INIT:
		LOG_INF("System initialization event received");
		break;
		
	case SYSTEM_EVENT_READY:
		LOG_INF("System ready event received");
		if (current_state == APP_STATE_STARTING) {
			current_state = APP_STATE_RUNNING;
			LOG_INF("Application is now running");
		}
		break;
		
	case SYSTEM_EVENT_ERROR:
		LOG_ERR("System error event: %s", event->message);
		current_state = APP_STATE_ERROR;
		break;
		
	case SYSTEM_EVENT_SHUTDOWN:
		LOG_INF("System shutdown event received");
		current_state = APP_STATE_SHUTDOWN;
		app_shutdown();
		break;
		
	case SYSTEM_EVENT_LOW_POWER:
		LOG_WRN("Low power event received");
		/* Implement power saving measures */
		break;
		
	case SYSTEM_EVENT_CRITICAL_POWER:
		LOG_ERR("Critical power event received - shutting down");
		current_state = APP_STATE_SHUTDOWN;
		app_shutdown();
		break;
		
	default:
		LOG_WRN("Unknown system event type: %d", event->type);
		break;
	}
}

static void watchdog_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	/* Simple watchdog - check if modules are responsive */
	if (current_state == APP_STATE_RUNNING) {
		LOG_DBG("Watchdog check - system healthy");
		
		/* Check module health */
		if (!cellular_module_is_healthy() ||
		    !wifi_module_is_healthy() ||
		    !bridge_module_is_healthy()) {
			LOG_ERR("Module health check failed");
			
			struct system_event event = {
				.type = SYSTEM_EVENT_ERROR,
				.error_code = -EFAULT,
			};
			strncpy(event.message, "Module health check failed", 
				sizeof(event.message) - 1);
			
			events_publish_system(&event);
		}
	}
	
	/* Reschedule watchdog */
	k_work_reschedule(&watchdog_work, K_SECONDS(30));
}

static void stats_work_handler(struct k_work *work)
{
	ARG_UNUSED(work);
	
	if (current_state == APP_STATE_RUNNING) {
		/* Print periodic statistics */
		utils_print_network_stats();
		utils_print_memory_usage();
		
		LOG_INF("Uptime: %u seconds", utils_get_uptime_seconds());
	}
	
	/* Reschedule stats work */
	k_work_reschedule(&stats_work, K_MINUTES(5));
}

static int app_init(void)
{
	int ret;
	
	LOG_INF("Cellular WiFi Router v%d.%d.%d starting...",
		APP_VERSION_MAJOR, APP_VERSION_MINOR, APP_VERSION_PATCH);
	
	/* Initialize event system */
	ret = events_init();
	if (ret) {
		LOG_ERR("Failed to initialize event system: %d", ret);
		return ret;
	}
	
	/* Subscribe to system events */
	ret = zbus_chan_add_obs(&system_events_chan, &system_events_sub, K_MSEC(100));
	if (ret) {
		LOG_ERR("Failed to subscribe to system events: %d", ret);
		return ret;
	}
	
	/* Initialize work items */
	k_work_init_delayable(&watchdog_work, watchdog_work_handler);
	k_work_init_delayable(&stats_work, stats_work_handler);
	
	LOG_INF("Application initialization completed");
	return 0;
}

static int app_start_modules(void)
{
	int ret;
	
	LOG_INF("Starting application modules...");
	
	/* Start power module first */
	ret = power_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize power module: %d", ret);
		return ret;
	}
	
	/* Start UI module */
	ret = ui_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize UI module: %d", ret);
		return ret;
	}
	
	/* Start cellular module */
	ret = cellular_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize cellular module: %d", ret);
		return ret;
	}
	
	/* Start WiFi module */
	ret = wifi_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize WiFi module: %d", ret);
		return ret;
	}
	
	/* Start bridge module last */
	ret = bridge_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize bridge module: %d", ret);
		return ret;
	}
	
	/* Start periodic tasks */
	k_work_reschedule(&watchdog_work, K_SECONDS(30));
	k_work_reschedule(&stats_work, K_MINUTES(5));
	
	LOG_INF("All modules started successfully");
	return 0;
}

static void app_shutdown(void)
{
	LOG_INF("Shutting down application...");
	
	/* Cancel periodic work */
	k_work_cancel_delayable(&watchdog_work);
	k_work_cancel_delayable(&stats_work);
	
	/* Shutdown modules in reverse order */
	bridge_module_shutdown();
	wifi_module_shutdown();
	cellular_module_shutdown();
	ui_module_shutdown();
	power_module_shutdown();
	
	LOG_INF("Application shutdown completed");
	
	/* Reboot the system */
	k_sleep(K_SECONDS(2));
	sys_reboot(SYS_REBOOT_COLD);
}

/* ZBUS observer work handler */
static void system_events_work_handler(struct k_work *work)
{
	const struct zbus_channel *chan;
	
	if (zbus_sub_wait(&system_events_sub, &chan, K_FOREVER) == 0) {
		system_event_handler(chan);
	}
}

K_WORK_DEFINE(system_events_work, system_events_work_handler);

/* System events observer thread */
static void system_events_thread(void)
{
	while (true) {
		k_work_submit(&system_events_work);
	}
}

K_THREAD_DEFINE(system_events_tid, 1024, system_events_thread, NULL, NULL, NULL,
		K_PRIO_COOP(7), 0, 0);

int main(void)
{
	int ret;
	
	/* Initialize application */
	ret = app_init();
	if (ret) {
		LOG_ERR("Application initialization failed: %d", ret);
		return ret;
	}
	
	current_state = APP_STATE_STARTING;
	
	/* Publish initialization event */
	struct system_event init_event = {
		.type = SYSTEM_EVENT_INIT,
		.error_code = 0,
	};
	strncpy(init_event.message, "Application starting", 
		sizeof(init_event.message) - 1);
	
	events_publish_system(&init_event);
	
	/* Start all modules */
	ret = app_start_modules();
	if (ret) {
		LOG_ERR("Failed to start modules: %d", ret);
		
		struct system_event error_event = {
			.type = SYSTEM_EVENT_ERROR,
			.error_code = ret,
		};
		strncpy(error_event.message, "Module startup failed", 
			sizeof(error_event.message) - 1);
		
		events_publish_system(&error_event);
		return ret;
	}
	
	/* Publish ready event */
	struct system_event ready_event = {
		.type = SYSTEM_EVENT_READY,
		.error_code = 0,
	};
	strncpy(ready_event.message, "Application ready", 
		sizeof(ready_event.message) - 1);
	
	events_publish_system(&ready_event);
	
	LOG_INF("Cellular WiFi Router is running");
	
	/* Main application loop */
	while (current_state != APP_STATE_SHUTDOWN) {
		k_sleep(K_SECONDS(1));
		
		/* Handle error state */
		if (current_state == APP_STATE_ERROR) {
			LOG_ERR("Application in error state - attempting recovery");
			k_sleep(K_SECONDS(5));
			
			/* Simple recovery - restart modules */
			app_shutdown();
			break;
		}
	}
	
	return 0;
}
