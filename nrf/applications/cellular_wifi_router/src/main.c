/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/kernel.h>
#include <zephyr/device.h>
#include <zephyr/logging/log.h>
#include <zephyr/zbus/zbus.h>
#include <zephyr/settings/settings.h>

#include "events/router_events.h"
#include "modules/cellular_module.h"
#include "modules/wifi_module.h"
#include "modules/bridge_module.h"
#include "modules/ui_module.h"
#include "modules/power_module.h"

LOG_MODULE_REGISTER(main, CONFIG_LOG_DEFAULT_LEVEL);

/* System state machine */
enum system_state {
	STATE_INIT,
	STATE_STARTING_CELLULAR,
	STATE_STARTING_WIFI,
	STATE_STARTING_BRIDGE,
	STATE_RUNNING,
	STATE_ERROR,
	STATE_POWER_SAVE,
	STATE_SHUTDOWN,
};

static enum system_state current_state = STATE_INIT;
static struct k_work_delayable startup_work;
static struct k_work_delayable watchdog_work;

/* System event observer */
static void system_event_handler(const struct zbus_channel *chan)
{
	const struct system_event *evt = zbus_chan_const_msg(chan);
	
	LOG_INF("System event: %d", evt->type);
	
	switch (evt->type) {
	case SYSTEM_EVENT_STARTUP:
		LOG_INF("System startup initiated");
		k_work_schedule(&startup_work, K_NO_WAIT);
		break;
		
	case SYSTEM_EVENT_RESET_REQUESTED:
		LOG_WRN("System reset requested");
		sys_reboot(SYS_REBOOT_WARM);
		break;
		
	case SYSTEM_EVENT_CONFIG_MODE_ENTERED:
		LOG_INF("Entering configuration mode");
		/* TODO: Implement configuration mode */
		break;
		
	case SYSTEM_EVENT_ERROR:
		LOG_ERR("System error: %s", evt->error_message);
		current_state = STATE_ERROR;
		break;
		
	default:
		break;
	}
}

ZBUS_LISTENER_DEFINE(system_listener, system_event_handler);

/* Cellular event observer */
static void cellular_event_handler(const struct zbus_channel *chan)
{
	const struct cellular_event *evt = zbus_chan_const_msg(chan);
	
	LOG_INF("Cellular event: %d", evt->type);
	
	switch (evt->type) {
	case CELLULAR_EVENT_CONNECTED:
		LOG_INF("Cellular connected to %s, signal: %d", 
			evt->operator_name, evt->signal_strength);
		if (current_state == STATE_STARTING_CELLULAR) {
			current_state = STATE_STARTING_WIFI;
			wifi_module_start();
		}
		break;
		
	case CELLULAR_EVENT_DISCONNECTED:
		LOG_WRN("Cellular disconnected");
		break;
		
	case CELLULAR_EVENT_ERROR:
		LOG_ERR("Cellular error: %d", evt->error_code);
		current_state = STATE_ERROR;
		break;
		
	default:
		break;
	}
}

ZBUS_LISTENER_DEFINE(cellular_listener, cellular_event_handler);

/* WiFi event observer */
static void wifi_event_handler(const struct zbus_channel *chan)
{
	const struct wifi_event *evt = zbus_chan_const_msg(chan);
	
	LOG_INF("WiFi event: %d", evt->type);
	
	switch (evt->type) {
	case WIFI_EVENT_AP_ENABLED:
		LOG_INF("WiFi AP enabled: %s", evt->ssid);
		if (current_state == STATE_STARTING_WIFI) {
			current_state = STATE_STARTING_BRIDGE;
			bridge_module_start();
		}
		break;
		
	case WIFI_EVENT_CLIENT_CONNECTED:
		LOG_INF("WiFi client connected, total clients: %d", evt->client_count);
		break;
		
	case WIFI_EVENT_CLIENT_DISCONNECTED:
		LOG_INF("WiFi client disconnected, total clients: %d", evt->client_count);
		break;
		
	case WIFI_EVENT_AP_ERROR:
		LOG_ERR("WiFi AP error: %d", evt->error_code);
		current_state = STATE_ERROR;
		break;
		
	default:
		break;
	}
}

ZBUS_LISTENER_DEFINE(wifi_listener, wifi_event_handler);

/* Bridge event observer */
static void bridge_event_handler(const struct zbus_channel *chan)
{
	const struct bridge_event *evt = zbus_chan_const_msg(chan);
	
	LOG_INF("Bridge event: %d", evt->type);
	
	switch (evt->type) {
	case BRIDGE_EVENT_ENABLED:
		LOG_INF("Bridge enabled, ready for routing");
		if (current_state == STATE_STARTING_BRIDGE) {
			current_state = STATE_RUNNING;
			LOG_INF("Cellular WiFi Router is now running!");
		}
		break;
		
	case BRIDGE_EVENT_PACKET_FORWARDED:
		LOG_DBG("Packets forwarded: %u, bytes: %u", 
			evt->packets_forwarded, evt->bytes_forwarded);
		break;
		
	case BRIDGE_EVENT_ERROR:
		LOG_ERR("Bridge error: %d", evt->error_code);
		current_state = STATE_ERROR;
		break;
		
	default:
		break;
	}
}

ZBUS_LISTENER_DEFINE(bridge_listener, bridge_event_handler);

/* UI event observer */
static void ui_event_handler(const struct zbus_channel *chan)
{
	const struct ui_event *evt = zbus_chan_const_msg(chan);
	
	switch (evt->type) {
	case UI_EVENT_BUTTON_PRESSED:
		LOG_INF("Button %d pressed", evt->button_id);
		break;
		
	case UI_EVENT_LONG_PRESS:
		LOG_INF("Button %d long press (%u ms)", evt->button_id, evt->press_duration_ms);
		if (evt->button_id == CONFIG_CELLULAR_WIFI_ROUTER_BUTTON_CONFIG) {
			struct system_event sys_evt = {
				.type = SYSTEM_EVENT_CONFIG_MODE_ENTERED
			};
			zbus_chan_pub(&system_chan, &sys_evt, K_MSEC(100));
		} else if (evt->button_id == CONFIG_CELLULAR_WIFI_ROUTER_BUTTON_RESET) {
			struct system_event sys_evt = {
				.type = SYSTEM_EVENT_RESET_REQUESTED
			};
			zbus_chan_pub(&system_chan, &sys_evt, K_MSEC(100));
		}
		break;
		
	default:
		break;
	}
}

ZBUS_LISTENER_DEFINE(ui_listener, ui_event_handler);

/* Power event observer */
static void power_event_handler(const struct zbus_channel *chan)
{
	const struct power_event *evt = zbus_chan_const_msg(chan);
	
	LOG_INF("Power event: %d, battery: %d%%", evt->type, evt->battery_level_percent);
	
	switch (evt->type) {
	case POWER_EVENT_BATTERY_LOW:
		LOG_WRN("Battery low: %d%%", evt->battery_level_percent);
		/* TODO: Implement power saving measures */
		break;
		
	case POWER_EVENT_BATTERY_CRITICAL:
		LOG_ERR("Battery critical: %d%%, shutting down", evt->battery_level_percent);
		struct system_event sys_evt = {
			.type = SYSTEM_EVENT_SHUTDOWN
		};
		zbus_chan_pub(&system_chan, &sys_evt, K_MSEC(100));
		break;
		
	case POWER_EVENT_POWER_SAVE_ENTERED:
		LOG_INF("Entered power save mode");
		current_state = STATE_POWER_SAVE;
		break;
		
	case POWER_EVENT_POWER_SAVE_EXITED:
		LOG_INF("Exited power save mode");
		current_state = STATE_RUNNING;
		break;
		
	default:
		break;
	}
}

ZBUS_LISTENER_DEFINE(power_listener, power_event_handler);

/* Startup work handler */
static void startup_work_handler(struct k_work *work)
{
	LOG_INF("Starting cellular WiFi router...");
	
	/* Initialize settings */
	int ret = settings_load();
	if (ret) {
		LOG_WRN("Failed to load settings: %d", ret);
	}
	
	/* Start modules in sequence */
	current_state = STATE_STARTING_CELLULAR;
	
	ret = cellular_module_start();
	if (ret) {
		LOG_ERR("Failed to start cellular module: %d", ret);
		current_state = STATE_ERROR;
		return;
	}
	
	/* WiFi and bridge will be started by event handlers */
}

/* Watchdog work handler */
static void watchdog_work_handler(struct k_work *work)
{
	/* System health check */
	LOG_DBG("System state: %d", current_state);
	
	if (current_state == STATE_ERROR) {
		LOG_ERR("System in error state, attempting recovery...");
		/* TODO: Implement error recovery */
	}
	
	/* Schedule next watchdog check */
	k_work_schedule(&watchdog_work, K_SECONDS(30));
}

int main(void)
{
	int ret;
	
	LOG_INF("Cellular WiFi Router starting...");
	LOG_INF("Build time: " __DATE__ " " __TIME__);
	
	/* Initialize work items */
	k_work_init_delayable(&startup_work, startup_work_handler);
	k_work_init_delayable(&watchdog_work, watchdog_work_handler);
	
	/* Initialize modules */
	ret = cellular_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize cellular module: %d", ret);
		return ret;
	}
	
	ret = wifi_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize WiFi module: %d", ret);
		return ret;
	}
	
	ret = bridge_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize bridge module: %d", ret);
		return ret;
	}
	
#ifdef CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED
	ret = ui_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize UI module: %d", ret);
		return ret;
	}
#endif
	
	ret = power_module_init();
	if (ret) {
		LOG_ERR("Failed to initialize power module: %d", ret);
		return ret;
	}
	
	/* Publish startup event */
	struct system_event startup_evt = {
		.type = SYSTEM_EVENT_STARTUP
	};
	zbus_chan_pub(&system_chan, &startup_evt, K_MSEC(100));
	
	/* Start watchdog */
	k_work_schedule(&watchdog_work, K_SECONDS(30));
	
	LOG_INF("Main initialization complete");
	
	return 0;
}
