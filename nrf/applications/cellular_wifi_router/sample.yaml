sample:
  name: Cellular WiFi Router
  description: >
    Application that creates a cellular IoT WiFi router using Thingy:91 X,
    bridging cellular (LTE-M/NB-IoT) and WiFi networks with modular architecture
    using ZBUS and SMF.

common:
  sysbuild: true
  integration_platforms:
    - thingy91x/nrf9151/cpuapp
  platform_allow:
    - thingy91x/nrf9151/cpuapp
  tags:
    - cellular
    - wifi
    - router
    - bridge
    - iot
    - lte
    - softap
    - zbus
    - smf

tests:
  applications.cellular_wifi_router.thingy91x_nrf9151_cpuapp:
    build_only: true
    platform_allow:
      - thingy91x/nrf9151/cpuapp
    integration_platforms:
      - thingy91x/nrf9151/cpuapp
    tags:
      - cellular
      - wifi
      - router
