sample:
  name: Cellular WiFi Router
  description: >
    A modular cellular WiFi router application for Thingy:91 X that bridges
    cellular connectivity to WiFi Access Point, enabling internet sharing
    for multiple WiFi clients.

common:
  tags:
    - cellular
    - wifi
    - router
    - bridge
    - nat
    - iot
    - thingy91x
  integration_platforms:
    - thingy91x/nrf9151/ns
  platform_allow:
    - thingy91x/nrf9151/ns
  depends_on:
    - lte_link_control
    - wifi
    - zbus
    - smf

tests:
  applications.cellular_wifi_router.thingy91x_nrf9151_ns:
    build_only: true
    platform_allow:
      - thingy91x/nrf9151/ns
    tags:
      - cellular
      - wifi
      - router
    extra_args:
      - CONFIG_LOG_DEFAULT_LEVEL=3
      - CONFIG_CELLULAR_WIFI_ROUTER=y
