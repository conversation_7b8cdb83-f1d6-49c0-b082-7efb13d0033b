# Zephyr Debugging Process for Cellular WiFi Router

This document outlines the debugging process for the Cellular WiFi Router application running on Thingy:91 X.

## Hardware Setup

### Required Hardware
- Thingy:91 X development kit
- nRF9151 DK (as external debugger)
- 10-pin SWD cable
- USB cables
- SIM card with data plan

### Hardware Connections
1. Set Thingy:91 X SWD selection switch (SW2) to **nRF91** position
2. Connect 10-pin SWD cable between Thingy:91 X debug port and nRF9151 DK
3. Connect nRF9151 DK to PC via USB
4. Power on both devices
5. Insert SIM card into Thingy:91 X

## Build Process

### Environment Setup
```bash
# Ensure NCS toolchain is in PATH
export PATH="../toolchains/ef4fc6722e/bin/:$PATH"

# Navigate to application directory
cd /opt/nordic/ncs/v3.0.2/nrf/applications/cellular_wifi_router
```

### Build Commands
```bash
# Clean build
west build -b thingy91x/nrf9151/ns --pristine

# Regular build
west build -b thingy91x/nrf9151/ns

# Build with debug symbols
west build -b thingy91x/nrf9151/ns -- -DCONFIG_DEBUG_INFO=y -DCONFIG_DEBUG_OPTIMIZATIONS=y

# Build with specific log level
west build -b thingy91x/nrf9151/ns -- -DCONFIG_LOG_DEFAULT_LEVEL=4
```

## Flashing Process

### Using nrfutil device
```bash
# Flash the application
west flash --runner nrfutil-device

# Alternative: Flash merged hex
nrfutil device program --firmware build/zephyr/merged.hex --chiperase
```

### Using JLink (if available)
```bash
# Flash using JLink
west flash --runner jlink
```

## Log Reading and Analysis

### Serial Connection Setup
```bash
# Find the correct tty device (usually tty.usbmodem*)
ls /dev/tty.usbmodem*

# Connect to serial port (adjust device name as needed)
screen /dev/tty.usbmodem1134202 115200

# Alternative: Use minicom
minicom -D /dev/tty.usbmodem1134202 -b 115200
```

### Log Analysis Workflow

#### 1. Boot Sequence Verification
Look for these key log messages during boot:
```
[00:00:00.123,456] <inf> main: Cellular WiFi Router v1.0.0 starting...
[00:00:00.234,567] <inf> events: Initializing event system
[00:00:00.345,678] <inf> ui_module: Initializing UI module
[00:00:00.456,789] <inf> cellular_module: Initializing cellular module
[00:00:00.567,890] <inf> wifi_module: Initializing WiFi module
[00:00:00.678,901] <inf> bridge_module: Initializing bridge module
[00:00:00.789,012] <inf> main: Cellular WiFi Router is running
```

#### 2. Cellular Connection Analysis
Monitor cellular connection progress:
```
[00:00:05.123,456] <inf> cellular_module: Configuring cellular modem
[00:00:05.234,567] <inf> cellular_module: LTE connection started
[00:00:10.345,678] <inf> cellular_module: Searching for cellular network
[00:00:15.456,789] <inf> cellular_module: Connected to cellular network
[00:00:15.567,890] <inf> cellular_module: PDN activated: CID 0
```

#### 3. WiFi AP Status Monitoring
Check WiFi Access Point startup:
```
[00:00:20.123,456] <inf> wifi_module: Starting WiFi Access Point
[00:00:20.234,567] <inf> wifi_module: WiFi AP started: SSID=Thingy91X-Router
[00:00:20.345,678] <inf> wifi_module: DHCP server started: 192.168.4.1
```

#### 4. Bridge Operation Verification
Monitor packet forwarding:
```
[00:00:30.123,456] <inf> bridge_module: Bridge started successfully
[00:00:35.234,567] <dbg> bridge_module: Packet forwarded: cellular->wifi
[00:00:35.345,678] <dbg> bridge_module: NAT entry added: 192.168.4.2:1234 -> 10.0.0.1:5678
```

## Error Analysis and Troubleshooting

### Common Error Patterns

#### 1. Cellular Connection Issues
```
# SIM card not detected
<err> cellular_module: Failed to initialize PDN: -2

# Network registration failure
<err> cellular_module: Network registration failed

# APN configuration issues
<err> cellular_module: Failed to configure PDN context: -22
```

**Solutions:**
- Verify SIM card is properly inserted
- Check APN configuration in prj.conf
- Verify cellular coverage in area
- Check antenna connections

#### 2. WiFi AP Startup Issues
```
# nRF7002 initialization failure
<err> wifi_module: Failed to initialize WiFi device: -19

# Channel configuration issues
<err> wifi_module: Invalid channel 6 in 1 band

# DHCP server startup failure
<err> wifi_module: DHCPv4 server failed to start: -16
```

**Solutions:**
- Verify nRF7002 power supply (check regulators)
- Check WiFi channel configuration
- Verify network interface setup
- Check memory allocation for DHCP pool

#### 3. Bridge/NAT Issues
```
# Interface binding failure
<err> bridge_module: Failed to bind interfaces: -22

# NAT table overflow
<err> bridge_module: NAT table full, dropping packet

# Routing issues
<err> bridge_module: No route to destination: 8.8.8.8
```

**Solutions:**
- Verify both interfaces are up and configured
- Increase NAT table size in configuration
- Check routing table setup
- Verify default gateway configuration

### Memory Issues
```
# Heap exhaustion
<err> main: Failed to allocate memory: -12

# Stack overflow
<err> os: ***** USAGE FAULT *****
<err> os: ***** Stack overflow *****
```

**Solutions:**
- Increase heap size: `CONFIG_HEAP_MEM_POOL_SIZE`
- Increase stack sizes for threads
- Check for memory leaks in modules
- Optimize buffer pool sizes

## Debug Commands and Tools

### Runtime Debug Commands
If shell is enabled, use these commands:
```bash
# Network interface status
net iface

# Network statistics
net stats

# WiFi status
wifi status

# Cellular status (if AT commands enabled)
at AT+CEREG?
at AT+CGDCONT?
```

### Memory Analysis
```bash
# Check memory usage
kernel stacks
kernel threads
```

### Log Level Control
```bash
# Increase log level for specific module
log level set cellular_module 4
log level set wifi_module 4
log level set bridge_module 4
```

## Performance Monitoring

### Key Metrics to Monitor
1. **Memory Usage**: Free heap, stack usage
2. **Network Throughput**: Bytes forwarded, packet loss
3. **Connection Stability**: Reconnection attempts, error rates
4. **Power Consumption**: Battery voltage, charging status

### Periodic Health Checks
The application includes built-in health monitoring:
```
[00:05:00.123,456] <inf> main: Watchdog check - system healthy
[00:05:00.234,567] <inf> utils: Memory usage: Free heap: 8192 bytes
[00:05:00.345,678] <inf> utils: Uptime: 00:05:00
```

## Recovery Procedures

### Soft Recovery
1. Check module health status
2. Restart failed modules individually
3. Reset network interfaces
4. Clear NAT table and restart bridge

### Hard Recovery
1. System reboot via watchdog
2. Factory reset configuration
3. Re-flash firmware
4. Hardware reset

## Debugging Best Practices

1. **Start with Clean Build**: Always use `--pristine` for debugging
2. **Enable Debug Logs**: Set appropriate log levels for troubleshooting
3. **Monitor Boot Sequence**: Verify all modules initialize correctly
4. **Check Hardware First**: Ensure proper connections and power
5. **Isolate Issues**: Test cellular and WiFi separately before bridge
6. **Use Incremental Testing**: Enable features one by one
7. **Document Findings**: Keep track of working configurations

## Advanced Debugging

### Using GDB (if available)
```bash
# Start GDB session
west debug

# Set breakpoints
(gdb) break main
(gdb) break cellular_module_init

# Examine variables
(gdb) print current_state
(gdb) print config
```

### RTT Logging (if enabled)
```bash
# Connect to RTT
JLinkRTTClient

# Or use nrfutil
nrfutil trace rtt
```

This debugging process should help identify and resolve issues systematically during development and testing of the Cellular WiFi Router application.
