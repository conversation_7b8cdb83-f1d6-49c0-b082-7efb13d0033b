.. _cellular_wifi_router:

Cellular WiFi Router
####################

.. contents::
   :local:
   :depth: 2

This application demonstrates how to use the Thingy:91 X as a cellular IoT WiFi router, providing internet connectivity to WiFi clients through a cellular connection.

Overview
********

The Cellular WiFi Router application creates a bridge between cellular (LTE-M/NB-IoT) and WiFi networks, allowing WiFi devices to access the internet through the cellular connection. The application uses a modular architecture with ZBUS for inter-module communication and SMF (State Machine Framework) for robust state management.

Key Features:

* **Cellular Connectivity**: LTE-M/NB-IoT connection with automatic reconnection
* **WiFi Access Point**: SoftAP mode with DHCP server for client devices
* **Network Bridge**: Packet forwarding and basic NAT functionality
* **Power Management**: Battery monitoring and power saving modes
* **User Interface**: LED status indicators and button controls (configurable)
* **Modular Design**: ZBUS and SMF-based architecture for maintainability

Requirements
************

The sample supports the following development kits:

.. table-from-sample-yaml::

Hardware Setup
**************

1. Insert a SIM card with data plan into the Thingy:91 X
2. Ensure the device is charged or connected to power
3. Place the device in an area with good cellular coverage

Building and Running
********************

.. |sample path| replace:: :file:`applications/cellular_wifi_router`

.. include:: /includes/build_and_run.txt

To build for Thingy:91 X:

.. code-block:: console

   west build -b thingy91x/nrf9151/cpuapp

Configuration
*************

The application can be configured using Kconfig options:

WiFi Configuration
==================

* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_WIFI_SSID`: WiFi AP SSID (default: "CellularRouter")
* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_WIFI_PASSWORD`: WiFi AP password (default: "RouterPass123")
* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_WIFI_CHANNEL`: WiFi channel (default: 6)

Network Configuration
=====================

* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_DHCP_POOL_START`: DHCP pool start address (default: "***********")
* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_DHCP_POOL_SIZE`: DHCP pool size (default: 10)
* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_MAX_CLIENTS`: Maximum WiFi clients (default: 10)

UI Configuration
================

* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED`: Enable UI (LEDs and buttons) (default: y)
* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_LED_CELLULAR`: LED for cellular status (default: 0)
* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_LED_WIFI`: LED for WiFi status (default: 1)
* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_LED_BRIDGE`: LED for bridge status (default: 2)
* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_LED_POWER`: LED for power status (default: 3)

Power Management
================

* :kconfig:option:`CONFIG_CELLULAR_WIFI_ROUTER_POWER_SAVE_TIMEOUT`: Power save timeout in seconds (default: 300)

User Interface
**************

LEDs
====

When UI is enabled, the LEDs indicate the following states:

* **LED 0 (Cellular)**: 
  - Off: Disconnected
  - Fast blink: Connecting
  - On: Connected
  - Slow blink: Error

* **LED 1 (WiFi)**:
  - Off: AP disabled
  - Fast blink: Starting AP
  - On: AP enabled
  - Pulse: Client connected
  - Slow blink: Error

* **LED 2 (Bridge)**:
  - Off: Bridge disabled
  - Fast blink: Starting bridge
  - On: Bridge enabled
  - Pulse: Packet forwarding active
  - Slow blink: Error

* **LED 3 (Power)**:
  - On: Normal power
  - Slow blink: Low battery
  - Fast blink: Critical battery
  - Pulse: Charging

Buttons
=======

* **Button 0 (Config)**: Long press to enter configuration mode
* **Button 1 (Reset)**: Long press to reset the device

Operation
*********

Startup Sequence
================

1. **Initialization**: All modules are initialized
2. **Cellular Connection**: Device connects to cellular network
3. **WiFi AP Start**: WiFi Access Point is enabled
4. **Bridge Enable**: Network bridge is activated
5. **Ready**: Router is operational

Normal Operation
================

1. WiFi clients can connect using the configured SSID and password
2. Clients receive IP addresses from the DHCP server (192.168.4.x range)
3. Internet traffic is routed through the cellular connection
4. LEDs indicate the status of each subsystem
5. Power management monitors battery and enters power save mode when idle

Power Management
================

The router implements several power saving features:

* **UI Disable**: LEDs and buttons can be disabled to save power
* **Idle Detection**: Automatic power save mode after configured timeout
* **Battery Monitoring**: Continuous battery level monitoring
* **Low Power Alerts**: Warnings when battery is low or critical

Troubleshooting
***************

Common Issues
=============

**Cellular Connection Fails**
  - Check SIM card installation
  - Verify cellular coverage
  - Check APN settings if required

**WiFi Clients Cannot Connect**
  - Verify SSID and password configuration
  - Check WiFi channel conflicts
  - Ensure maximum client limit not exceeded

**No Internet Access**
  - Verify cellular data connection
  - Check NAT/routing configuration
  - Monitor bridge statistics

**High Power Consumption**
  - Disable UI if not needed
  - Reduce power save timeout
  - Check for continuous packet forwarding

Debugging
=========

Enable debug logging for specific modules:

.. code-block:: console

   CONFIG_LOG_DEFAULT_LEVEL=4
   CONFIG_CELLULAR_MODULE_LOG_LEVEL_DBG=y
   CONFIG_WIFI_MODULE_LOG_LEVEL_DBG=y
   CONFIG_BRIDGE_MODULE_LOG_LEVEL_DBG=y

Architecture
************

The application uses a modular architecture with the following components:

Modules
=======

* **Cellular Module**: Manages LTE-M/NB-IoT connection using SMF state machine
* **WiFi Module**: Manages WiFi SoftAP and DHCP server using SMF state machine  
* **Bridge Module**: Handles packet forwarding and NAT using SMF state machine
* **UI Module**: Manages LEDs and buttons with automatic status indication
* **Power Module**: Monitors battery and implements power saving features

Communication
=============

* **ZBUS**: Inter-module communication using publish/subscribe pattern
* **Events**: Structured event system for status updates and coordination
* **State Machines**: SMF-based state machines for robust module behavior

Dependencies
************

This sample uses the following libraries:

* :ref:`lte_lc_readme`
* :ref:`pdn_readme`
* :ref:`modem_info_readme`
* :ref:`dk_buttons_and_leds_readme`
* :ref:`zbus`
* :ref:`smf`
