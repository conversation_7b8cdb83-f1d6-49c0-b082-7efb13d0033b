.. _cellular_wifi_router:

Cellular WiFi Router
####################

.. contents::
   :local:
   :depth: 2

This application demonstrates how to create a cellular WiFi router using the Thingy:91 X development kit. The application bridges cellular connectivity (LTE-M/NB-IoT) to a WiFi Access Point, enabling internet sharing for multiple WiFi clients.

Overview
********

The Cellular WiFi Router application is built using a modular architecture with the following key components:

* **Cellular Module**: Manages LTE-M/NB-IoT connectivity using the nRF9151 modem
* **WiFi Module**: Provides WiFi Access Point functionality using the nRF7002 companion IC
* **Bridge Module**: Implements packet forwarding and NAT between cellular and WiFi interfaces
* **UI Module**: Controls LEDs and buttons with power-optimized operation
* **Power Module**: Manages battery monitoring and power optimization

The application uses ZBUS for inter-module communication and SMF (State Machine Framework) for robust state management.

Requirements
************

* Thingy:91 X development kit
* nRF9151 DK (for debugging)
* SIM card with data plan
* 10-pin SWD cable
* Nordic Connect SDK v3.0.2 or later

Features
********

Cellular Connectivity
=====================

* LTE-M and NB-IoT support
* Automatic network registration
* Signal strength monitoring
* Power saving mode support
* Connection retry logic

WiFi Access Point
=================

* WiFi 6 support via nRF7002
* WPA2-PSK security
* DHCP server for client IP assignment
* Up to 10 concurrent clients
* Configurable SSID and password

Network Bridge
==============

* Packet forwarding between cellular and WiFi
* NAT (Network Address Translation)
* Basic firewall functionality
* Traffic statistics
* Connection tracking

Power Management
================

* Battery voltage monitoring
* Automatic power saving modes
* UI disable for power optimization
* Low battery warnings
* Charging status detection

User Interface
==============

* Status LEDs (cellular, WiFi, system)
* Control button for mode switching
* LED patterns for different states
* Power-optimized operation

Configuration
*************

The application can be configured through several methods:

Device Tree Overlay
===================

The ``boards/thingy91x_nrf9151_ns.overlay`` file contains hardware-specific configurations:

.. code-block:: devicetree

   network_config {
       wifi-ssid = "Thingy91X-Router";
       wifi-password = "Nordic123";
       wifi-channel = <6>;
       wifi-max-clients = <10>;
   };

Project Configuration
=====================

Key configuration options in ``prj.conf``:

.. code-block:: kconfig

   # Disable bootloaders to save memory
   CONFIG_BOOTLOADER_MCUBOOT=n
   CONFIG_SECURE_BOOT=n
   
   # ZBUS for inter-module communication
   CONFIG_ZBUS=y
   
   # State Machine Framework
   CONFIG_SMF=y
   
   # WiFi SoftAP mode
   CONFIG_WIFI=y
   CONFIG_WIFI_NRF70=y
   
   # Cellular connectivity
   CONFIG_LTE_LINK_CONTROL=y
   CONFIG_PDN=y

Board-Specific Configuration
============================

The ``boards/thingy91x_nrf9151_ns.conf`` file contains Thingy:91 X specific settings:

.. code-block:: kconfig

   # Hardware specific configurations
   CONFIG_HW_STACK_PROTECTION=y
   CONFIG_HW_ID_LIBRARY_SOURCE_IMEI=y
   
   # WiFi specific for Thingy:91 X
   CONFIG_WIFI=y
   CONFIG_WIFI_NRF70=y

Building and Running
********************

Building
========

To build the application for Thingy:91 X:

.. code-block:: console

   cd nrf/applications/cellular_wifi_router
   west build -b thingy91x/nrf9151/ns

For a clean build:

.. code-block:: console

   west build -b thingy91x/nrf9151/ns --pristine

Programming
===========

1. Set the Thingy:91 X SWD selection switch (SW2) to **nRF91** position
2. Connect the 10-pin SWD cable between Thingy:91 X and nRF9151 DK
3. Connect nRF9151 DK to PC via USB
4. Flash the application:

.. code-block:: console

   west flash --runner nrfutil-device

Testing
*******

1. **Insert SIM Card**: Insert a SIM card with data plan into the Thingy:91 X
2. **Power On**: Power on the device and observe the LED patterns
3. **Cellular Connection**: Wait for cellular connection (blue LED solid)
4. **WiFi AP**: Wait for WiFi AP to start (green LED solid)
5. **Connect Client**: Connect a WiFi device to "Thingy91X-Router" with password "Nordic123"
6. **Test Internet**: Verify internet connectivity through the router

LED Indicators
==============

* **Red LED (Status)**: System status
  
  * Off: System initializing
  * Slow blink: System ready
  * Fast blink: Error state
  * Solid: System running

* **Blue LED (Cellular)**: Cellular connection status
  
  * Off: Cellular disabled
  * Slow blink: Searching for network
  * Fast blink: Connecting
  * Solid: Connected

* **Green LED (WiFi)**: WiFi AP status
  
  * Off: WiFi AP disabled
  * Slow blink: Starting AP
  * Solid: AP active
  * Fast blink: AP error

Button Controls
===============

* **Short Press**: Toggle between power modes
* **Long Press (5s)**: Factory reset configuration
* **Double Press**: Enable/disable UI for power saving

Debugging
*********

Serial Console
==============

Connect to the serial console to view logs:

.. code-block:: console

   screen /dev/tty.usbmodem1134202 115200

Key log messages to monitor:

.. code-block:: console

   [00:00:00.123] <inf> main: Cellular WiFi Router v1.0.0 starting...
   [00:00:05.234] <inf> cellular_module: Connected to cellular network
   [00:00:10.345] <inf> wifi_module: WiFi AP started: SSID=Thingy91X-Router
   [00:00:15.456] <inf> bridge_module: Bridge started successfully

For detailed debugging instructions, see ``.augment/rules/zephyr-debugging-process.md``.

Troubleshooting
***************

Common Issues
=============

**Cellular Connection Fails**

* Verify SIM card is properly inserted
* Check cellular coverage in your area
* Verify APN configuration (if required)
* Check antenna connections

**WiFi AP Not Starting**

* Verify nRF7002 power supply
* Check WiFi channel configuration
* Ensure sufficient memory allocation
* Verify device tree configuration

**No Internet Through Router**

* Verify both cellular and WiFi are connected
* Check bridge module status
* Verify NAT table entries
* Check routing configuration

**High Power Consumption**

* Enable power saving mode
* Disable UI when not needed
* Reduce WiFi beacon interval
* Use cellular power saving features

Memory Issues
=============

If experiencing memory issues:

* Increase heap size: ``CONFIG_HEAP_MEM_POOL_SIZE``
* Reduce buffer pool sizes
* Disable unused features
* Optimize log levels

Performance Optimization
************************

Network Performance
===================

* Use appropriate cellular mode (LTE-M vs NB-IoT)
* Optimize WiFi channel selection
* Limit number of concurrent clients
* Implement QoS if needed

Power Optimization
==================

* Enable automatic power saving
* Reduce LED brightness
* Use cellular PSM (Power Saving Mode)
* Implement idle timeouts

Memory Optimization
===================

* Disable unused crypto algorithms
* Reduce network buffer counts
* Optimize log levels for production
* Use static memory allocation where possible

Architecture Details
********************

Module Communication
====================

The application uses ZBUS for event-driven communication between modules:

.. code-block:: c

   // Event publishing
   struct cellular_event event = {
       .type = CELLULAR_EVENT_CONNECTED,
       .iface = cellular_iface,
   };
   events_publish_cellular(&event);

State Management
================

Each module uses SMF for robust state management:

.. code-block:: c

   enum cellular_state {
       CELLULAR_STATE_DISABLED,
       CELLULAR_STATE_CONNECTING,
       CELLULAR_STATE_CONNECTED,
       CELLULAR_STATE_ERROR,
   };

The modular architecture ensures clean separation of concerns and makes the application maintainable and extensible.

References
**********

* `Nordic Connect SDK Documentation`_
* `Thingy:91 X User Guide`_
* `nRF9151 Product Specification`_
* `nRF7002 Product Specification`_

.. _Nordic Connect SDK Documentation: https://docs.nordicsemi.com/
.. _Thingy:91 X User Guide: https://docs.nordicsemi.com/
.. _nRF9151 Product Specification: https://docs.nordicsemi.com/
.. _nRF7002 Product Specification: https://docs.nordicsemi.com/
