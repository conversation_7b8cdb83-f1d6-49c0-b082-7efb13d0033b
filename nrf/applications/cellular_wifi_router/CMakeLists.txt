#
# Copyright (c) 2024 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

cmake_minimum_required(VERSION 3.20.0)

find_package(Zephyr REQUIRED HINTS $ENV{ZEPHYR_BASE})
project(cellular_wifi_router)

# NORDIC SDK APP START
target_sources(app PRIVATE
    src/main.c
    src/modules/cellular_module.c
    src/modules/wifi_module.c
    src/modules/bridge_module.c
    src/modules/ui_module.c
    src/modules/power_module.c
    src/events/router_events.c
)

target_include_directories(app PRIVATE
    src/
    src/modules/
    src/events/
)

# NORDIC SDK APP END
