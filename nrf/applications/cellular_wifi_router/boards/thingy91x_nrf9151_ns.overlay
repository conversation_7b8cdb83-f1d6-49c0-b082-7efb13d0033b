/*
 * Thingy:91 X Device Tree Overlay for Cellular WiFi Router
 * Copyright (c) 2024 Nordic Semiconductor ASA
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

#include <zephyr/dt-bindings/gpio/gpio.h>
#include <zephyr/dt-bindings/led/led.h>

/ {
	chosen {
		zephyr,wifi = &nordic_wlan0;
	};

	/* Router buttons */
	router_buttons {
		compatible = "gpio-keys";

		router_button0: router_button_0 {
			gpios = <&gpio0 8 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
			label = "Router Control Button";
			zephyr,code = <0>;
		};
	};

	/* Router LEDs */
	router_leds {
		compatible = "gpio-leds";

		/* Status LED - Red */
		router_led_status: router_led_0 {
			gpios = <&gpio0 29 GPIO_ACTIVE_LOW>;
			label = "Status LED";
		};

		/* Cellular LED - Blue */
		router_led_cellular: router_led_1 {
			gpios = <&gpio0 30 GPIO_ACTIVE_LOW>;
			label = "Cellular LED";
		};

		/* WiFi LED - Green */
		router_led_wifi: router_led_2 {
			gpios = <&gpio0 31 GPIO_ACTIVE_LOW>;
			label = "WiFi LED";
		};
	};

	/* PWM LEDs for brightness control */
	router_pwm_leds {
		compatible = "pwm-leds";

		router_pwm_led_status: router_pwm_led_0 {
			pwms = <&pwm0 0 PWM_MSEC(20) PWM_POLARITY_INVERTED>;
			label = "PWM Status LED";
		};

		router_pwm_led_cellular: router_pwm_led_1 {
			pwms = <&pwm0 1 PWM_MSEC(20) PWM_POLARITY_INVERTED>;
			label = "PWM Cellular LED";
		};

		router_pwm_led_wifi: router_pwm_led_2 {
			pwms = <&pwm0 2 PWM_MSEC(20) PWM_POLARITY_INVERTED>;
			label = "PWM WiFi LED";
		};
	};
};

/* Enable WiFi */
&nrf70 {
	status = "okay";
};

/* Configure PWM for LED control */
&pwm0 {
	status = "okay";
	pinctrl-0 = <&pwm0_default>;
	pinctrl-1 = <&pwm0_sleep>;
	pinctrl-names = "default", "sleep";
};

/* Enable I2C for sensors (optional) */
&i2c2 {
	status = "okay";
	pinctrl-0 = <&i2c2_default>;
	pinctrl-1 = <&i2c2_sleep>;
	pinctrl-names = "default", "sleep";
};

/* Enable SPI for external flash */
&spi3 {
	status = "okay";
	pinctrl-0 = <&spi3_default>;
	pinctrl-1 = <&spi3_sleep>;
	pinctrl-names = "default", "sleep";
};

/* Enable UART for debugging */
&uart0 {
	status = "okay";
	current-speed = <115200>;
	pinctrl-0 = <&uart0_default>;
	pinctrl-1 = <&uart0_sleep>;
	pinctrl-names = "default", "sleep";
};

/* Power management regulators */
&pmic_main {
	status = "okay";
	npm1300_gpios {
		status = "okay";
	};
	regulators {
		status = "okay";
	};
	charger {
		status = "okay";
	};
};
