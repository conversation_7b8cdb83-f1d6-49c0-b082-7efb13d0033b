#
# Copyright (c) 2024 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

# Configuration file for Thingy:91 X nRF9151 CPU App
# This file is merged with prj.conf in the application folder, and options
# set here will take precedence if they are present in both files.

# Board specific configurations
CONFIG_HW_STACK_PROTECTION=y
CONFIG_HW_ID_LIBRARY_SOURCE_IMEI=y
CONFIG_PICOLIBC=y

# Modem configurations
CONFIG_MODEM_INFO=y
CONFIG_AT_HOST_LIBRARY=y

# Modem trace (can be disabled for production)
CONFIG_NRF_MODEM_LIB_TRACE=n

# WiFi configurations for nRF7002
CONFIG_WIFI=y
CONFIG_WIFI_NRF70=y
CONFIG_WIFI_NRF70_SKIP_LOCAL_ADMIN_MAC=y
CONFIG_NRF_WIFI_SCAN_MAX_BSS_CNT=10

# Network configurations
CONFIG_NET_DEFAULT_IF_ETHERNET=y
CONFIG_NET_L2_ETHERNET=y
CONFIG_NET_NATIVE=y
CONFIG_NET_IPV4=y
CONFIG_NET_DHCPV4=y
CONFIG_NET_STATISTICS=y
CONFIG_NET_STATISTICS_WIFI=y
CONFIG_NET_STATISTICS_USER_API=y
CONFIG_NET_CONTEXT_SYNC_RECV=y

# Disable IPv6 to save memory
CONFIG_NET_IPV6=n
CONFIG_NET_IPV6_NBR_CACHE=n
CONFIG_NET_IPV6_MLD=n

# Power management
CONFIG_PM=y
CONFIG_PM_DEVICE=y
CONFIG_PM_DEVICE_RUNTIME=y

# External flash support
CONFIG_SPI=y
CONFIG_SPI_NOR=y
CONFIG_STREAM_FLASH_ERASE=y
CONFIG_FLASH=y

# Full modem update support
CONFIG_DFU_TARGET_FULL_MODEM=y
CONFIG_FMFU_FDEV=y
CONFIG_ZCBOR=y
CONFIG_FMFU_FDEV_SKIP_PREVALIDATION=n

# Sensors (can be disabled for power saving)
CONFIG_BME680=n
CONFIG_SENSOR=n

# Crypto optimizations
CONFIG_OBERON_BACKEND=y
CONFIG_NORDIC_SECURITY_BACKEND=y
CONFIG_MBEDTLS_CHACHA20_C=n
CONFIG_MBEDTLS_POLY1305_C=n
CONFIG_MBEDTLS_ECP_C=n
CONFIG_MBEDTLS_RSA_C=n
CONFIG_MBEDTLS_DHM_C=n
CONFIG_MBEDTLS_SHA1_C=n
CONFIG_MBEDTLS_SHA384_C=n
CONFIG_MBEDTLS_SHA512_C=n
CONFIG_MBEDTLS_SHA256_C=y

# Memory optimizations for nRF9151
CONFIG_MAIN_STACK_SIZE=3072
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=1536
CONFIG_NET_PKT_RX_COUNT=12
CONFIG_NET_PKT_TX_COUNT=12
CONFIG_NET_BUF_RX_COUNT=24
CONFIG_NET_BUF_TX_COUNT=24
CONFIG_HEAP_MEM_POOL_SIZE=12288
