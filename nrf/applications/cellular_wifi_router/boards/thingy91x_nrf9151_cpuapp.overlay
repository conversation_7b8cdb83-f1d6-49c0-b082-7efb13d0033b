/*
 * Copyright (c) 2024 Nordic Semiconductor ASA
 *
 * SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
 */

/ {
	chosen {
		zephyr,wifi = &nordic_wlan0;
	};

	/* Define aliases for UI elements */
	aliases {
		led0 = &led0;
		led1 = &led1;
		led2 = &led2;
		led3 = &led3;
		sw0 = &button0;
		sw1 = &button1;
	};

	/* LED definitions for status indication */
	leds {
		compatible = "gpio-leds";
		led0: led_0 {
			gpios = <&gpio0 29 GPIO_ACTIVE_LOW>;
			label = "Cellular Status LED";
		};
		led1: led_1 {
			gpios = <&gpio0 30 GPIO_ACTIVE_LOW>;
			label = "WiFi Status LED";
		};
		led2: led_2 {
			gpios = <&gpio0 31 GPIO_ACTIVE_LOW>;
			label = "Bridge Status LED";
		};
		led3: led_3 {
			gpios = <&gpio0 28 GPIO_ACTIVE_LOW>;
			label = "Power Status LED";
		};
	};

	/* Button definitions for user input */
	buttons {
		compatible = "gpio-keys";
		button0: button_0 {
			gpios = <&gpio0 23 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
			label = "Config Button";
		};
		button1: button_1 {
			gpios = <&gpio0 24 (GPIO_PULL_UP | GPIO_ACTIVE_LOW)>;
			label = "Reset Button";
		};
	};

	/* PWM for LED brightness control (optional) */
	pwmleds {
		compatible = "pwm-leds";
		status = "disabled"; /* Enable if PWM control needed */
		pwm_led0: pwm_led_0 {
			pwms = <&pwm0 0 PWM_MSEC(20) PWM_POLARITY_INVERTED>;
		};
	};
};

/* Enable WiFi */
&nrf70 {
	status = "okay";
};

/* Enable SPI for WiFi */
&spi3 {
	status = "okay";
};

/* Enable I2C for sensors (optional) */
&i2c2 {
	status = "disabled"; /* Enable if sensors needed */
};

/* Enable PWM for LED control */
&pwm0 {
	status = "okay";
	pinctrl-0 = <&pwm0_default>;
	pinctrl-1 = <&pwm0_sleep>;
	pinctrl-names = "default", "sleep";
};

/* GPIO configuration */
&gpio0 {
	status = "okay";
	/* Use PORT event rather than GPIOTE IN event, to save power */
	sense-edge-mask = <0xffffffff>;
};

/* UART configuration for debugging */
&uart0 {
	status = "okay";
	current-speed = <115200>;
	pinctrl-0 = <&uart0_default>;
	pinctrl-1 = <&uart0_sleep>;
	pinctrl-names = "default", "sleep";
};

/* Flash configuration */
&flash0 {
	partitions {
		compatible = "fixed-partitions";
		#address-cells = <1>;
		#size-cells = <1>;

		/* Reserve space for settings */
		storage_partition: partition@f8000 {
			label = "storage";
			reg = <0x000f8000 0x00008000>;
		};
	};
};

/* NVS storage for settings */
&storage_partition {
	status = "okay";
};
