# Thingy:91 X nRF9151 Non-Secure Configuration
# Copyright (c) 2024 Nordic Semiconductor ASA
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause

# Hardware specific configurations
CONFIG_HW_STACK_PROTECTION=y
CONFIG_HW_ID_LIBRARY_SOURCE_IMEI=y

# Modem configurations
CONFIG_MODEM_INFO=y
CONFIG_AT_HOST_LIBRARY=y

# Modem trace (disabled for production)
CONFIG_NRF_MODEM_LIB_TRACE=n

# WiFi specific for Thingy:91 X
CONFIG_WIFI=y
CONFIG_WIFI_NRF70=y

# Network interface configuration
CONFIG_NET_DEFAULT_IF_ETHERNET=y
CONFIG_NET_STATISTICS=y
CONFIG_NET_STATISTICS_WIFI=y
CONFIG_NET_STATISTICS_USER_API=y
CONFIG_NET_CONTEXT_SYNC_RECV=y

# Disable IPv6 features not needed for router
CONFIG_NET_IPV6_NBR_CACHE=n
CONFIG_NET_IPV6_MLD=n

# Power management for Thingy:91 X
CONFIG_REGULATOR=y

# External flash support
CONFIG_SPI=y
CONFIG_FLASH=y

# Sensors (disabled for power saving)
CONFIG_SENSOR=n

# Crypto optimizations
CONFIG_OBERON_BACKEND=y
CONFIG_NORDIC_SECURITY_BACKEND=y
# Disable unused crypto to save memory
CONFIG_MBEDTLS_CHACHA20_C=n
CONFIG_MBEDTLS_POLY1305_C=n
CONFIG_MBEDTLS_ECP_C=n
CONFIG_MBEDTLS_RSA_C=n
CONFIG_MBEDTLS_DHM_C=n
CONFIG_MBEDTLS_SHA1_C=n
CONFIG_MBEDTLS_SHA384_C=n
CONFIG_MBEDTLS_SHA512_C=n
CONFIG_MBEDTLS_SHA256_C=y
