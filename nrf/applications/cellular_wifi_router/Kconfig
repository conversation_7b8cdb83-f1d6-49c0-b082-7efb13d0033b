# Cellular WiFi Router Application Configuration
# Copyright (c) 2024 Nordic Semiconductor ASA
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause

source "Kconfig.zephyr"

menu "Cellular WiFi Router Configuration"

config CELLULAR_WIFI_ROUTER
	bool "Enable Cellular WiFi Router application"
	default y
	help
	  Enable the Cellular WiFi Router application.

if CELLULAR_WIFI_ROUTER

menu "Cellular Configuration"

config CELLULAR_WIFI_ROUTER_APN
	string "Default APN"
	default ""
	help
	  Default APN for cellular connection. Leave empty for automatic APN.

config CELLULAR_WIFI_ROUTER_CELLULAR_TIMEOUT
	int "Cellular connection timeout (seconds)"
	default 120
	range 30 600
	help
	  Timeout for cellular connection establishment.

config CELLULAR_WIFI_ROUTER_CELLULAR_IDLE_TIMEOUT
	int "Cellular idle timeout (seconds)"
	default 300
	range 60 3600
	help
	  Timeout before entering cellular power saving mode.

config CELLULAR_WIFI_ROUTER_CELLULAR_AUTO_CONNECT
	bool "Automatic cellular connection"
	default y
	help
	  Automatically connect to cellular network on startup.

endmenu # Cellular Configuration

menu "WiFi Configuration"

config CELLULAR_WIFI_ROUTER_WIFI_SSID
	string "Default WiFi SSID"
	default "Thingy91X-Router"
	help
	  Default SSID for WiFi Access Point.

config CELLULAR_WIFI_ROUTER_WIFI_PASSWORD
	string "Default WiFi Password"
	default "Nordic123"
	help
	  Default password for WiFi Access Point.

config CELLULAR_WIFI_ROUTER_WIFI_CHANNEL
	int "Default WiFi Channel"
	default 6
	range 1 11
	help
	  Default WiFi channel for Access Point.

config CELLULAR_WIFI_ROUTER_WIFI_MAX_CLIENTS
	int "Maximum WiFi clients"
	default 10
	range 1 20
	help
	  Maximum number of concurrent WiFi clients.

config CELLULAR_WIFI_ROUTER_WIFI_HIDDEN
	bool "Hidden WiFi network"
	default n
	help
	  Make WiFi network hidden (SSID not broadcast).

endmenu # WiFi Configuration

menu "DHCP Configuration"

config CELLULAR_WIFI_ROUTER_DHCP_POOL_START
	string "DHCP pool start address"
	default "***********"
	help
	  Start IP address for DHCP pool.

config CELLULAR_WIFI_ROUTER_DHCP_POOL_END
	string "DHCP pool end address"
	default "*************"
	help
	  End IP address for DHCP pool.

config CELLULAR_WIFI_ROUTER_DHCP_GATEWAY
	string "DHCP gateway address"
	default "***********"
	help
	  Gateway IP address for DHCP clients.

config CELLULAR_WIFI_ROUTER_DHCP_NETMASK
	string "DHCP netmask"
	default "*************"
	help
	  Netmask for DHCP clients.

config CELLULAR_WIFI_ROUTER_DHCP_LEASE_TIME
	int "DHCP lease time (seconds)"
	default 3600
	range 300 86400
	help
	  DHCP lease time in seconds.

endmenu # DHCP Configuration

menu "Bridge Configuration"

config CELLULAR_WIFI_ROUTER_NAT_ENABLE
	bool "Enable NAT (Network Address Translation)"
	default y
	help
	  Enable NAT for packet forwarding between interfaces.

config CELLULAR_WIFI_ROUTER_NAT_TABLE_SIZE
	int "NAT table size"
	default 64
	range 16 256
	help
	  Maximum number of NAT entries.

config CELLULAR_WIFI_ROUTER_NAT_TIMEOUT
	int "NAT entry timeout (seconds)"
	default 300
	range 60 3600
	help
	  Timeout for inactive NAT entries.

config CELLULAR_WIFI_ROUTER_PACKET_LOGGING
	bool "Enable packet logging"
	default n
	help
	  Enable detailed packet logging for debugging.

endmenu # Bridge Configuration

menu "Power Management"

config CELLULAR_WIFI_ROUTER_BATTERY_LOW_THRESHOLD
	int "Low battery threshold (mV)"
	default 3300
	range 3000 3800
	help
	  Battery voltage threshold for low battery warning.

config CELLULAR_WIFI_ROUTER_BATTERY_CRITICAL_THRESHOLD
	int "Critical battery threshold (mV)"
	default 3000
	range 2800 3500
	help
	  Battery voltage threshold for critical battery warning.

config CELLULAR_WIFI_ROUTER_AUTO_POWER_SAVE
	bool "Automatic power saving"
	default y
	help
	  Automatically enter power saving mode when idle.

config CELLULAR_WIFI_ROUTER_SYSTEM_IDLE_TIMEOUT
	int "System idle timeout (seconds)"
	default 1800
	range 300 7200
	help
	  Timeout before entering system power saving mode.

config CELLULAR_WIFI_ROUTER_UI_DISABLE_LOW_POWER
	bool "Disable UI in low power mode"
	default y
	help
	  Disable UI (LEDs, buttons) when in low power mode.

endmenu # Power Management

menu "UI Configuration"

config CELLULAR_WIFI_ROUTER_LED_BRIGHTNESS_NORMAL
	int "Normal LED brightness (0-100)"
	default 50
	range 0 100
	help
	  LED brightness in normal operation mode.

config CELLULAR_WIFI_ROUTER_LED_BRIGHTNESS_LOW_POWER
	int "Low power LED brightness (0-100)"
	default 10
	range 0 100
	help
	  LED brightness in low power mode.

config CELLULAR_WIFI_ROUTER_BUTTON_ENABLE
	bool "Enable button control"
	default y
	help
	  Enable button for user interaction.

config CELLULAR_WIFI_ROUTER_LED_PATTERNS
	bool "Enable LED patterns"
	default y
	help
	  Enable different LED patterns for status indication.

endmenu # UI Configuration

menu "Debug and Logging"

config CELLULAR_WIFI_ROUTER_DEBUG
	bool "Enable debug features"
	default n
	help
	  Enable additional debug features and logging.

config CELLULAR_WIFI_ROUTER_STATS_ENABLE
	bool "Enable statistics collection"
	default y
	help
	  Enable collection of network and system statistics.

config CELLULAR_WIFI_ROUTER_STATS_INTERVAL
	int "Statistics update interval (seconds)"
	default 30
	range 5 300
	depends on CELLULAR_WIFI_ROUTER_STATS_ENABLE
	help
	  Interval for updating statistics.

config CELLULAR_WIFI_ROUTER_WATCHDOG_ENABLE
	bool "Enable watchdog monitoring"
	default y
	help
	  Enable watchdog for system health monitoring.

config CELLULAR_WIFI_ROUTER_WATCHDOG_TIMEOUT
	int "Watchdog timeout (seconds)"
	default 30
	range 10 120
	depends on CELLULAR_WIFI_ROUTER_WATCHDOG_ENABLE
	help
	  Watchdog timeout in seconds.

endmenu # Debug and Logging

endif # CELLULAR_WIFI_ROUTER

endmenu # Cellular WiFi Router Configuration
