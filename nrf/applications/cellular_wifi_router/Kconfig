#
# Copyright (c) 2024 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

source "Kconfig.zephyr"

menu "Cellular WiFi Router"

config CELLULAR_WIFI_ROUTER_UI_ENABLED
	bool "Enable UI (LEDs and buttons)"
	default y
	help
	  Enable user interface with LEDs and buttons.
	  Disable this option to save power in headless operation.

if CELLULAR_WIFI_ROUTER_UI_ENABLED

config CELLULAR_WIFI_ROUTER_LED_CELLULAR
	int "LED number for cellular status"
	default 0
	range 0 3
	help
	  LED number to indicate cellular connection status.

config CELLULAR_WIFI_ROUTER_LED_WIFI
	int "LED number for WiFi status"
	default 1
	range 0 3
	help
	  LED number to indicate WiFi AP status.

config CELLULAR_WIFI_ROUTER_LED_BRIDGE
	int "LED number for bridge status"
	default 2
	range 0 3
	help
	  LED number to indicate bridge/routing status.

config CELLULAR_WIFI_ROUTER_LED_POWER
	int "LED number for power status"
	default 3
	range 0 3
	help
	  LED number to indicate power/battery status.

config CELLULAR_WIFI_ROUTER_BUTTON_CONFIG
	int "Button number for configuration mode"
	default 0
	range 0 3
	help
	  But<PERSON> number to enter configuration mode.

config CELLULAR_WIFI_ROUTER_BUTTON_RESET
	int "Button number for reset"
	default 1
	range 0 3
	help
	  Button number to reset the router.

endif # CELLULAR_WIFI_ROUTER_UI_ENABLED

config CELLULAR_WIFI_ROUTER_WIFI_SSID
	string "WiFi AP SSID"
	default "CellularRouter"
	help
	  SSID for the WiFi Access Point.

config CELLULAR_WIFI_ROUTER_WIFI_PASSWORD
	string "WiFi AP Password"
	default "RouterPass123"
	help
	  Password for the WiFi Access Point.

config CELLULAR_WIFI_ROUTER_WIFI_CHANNEL
	int "WiFi AP Channel"
	default 6
	range 1 11
	help
	  WiFi channel for the Access Point.

config CELLULAR_WIFI_ROUTER_DHCP_POOL_START
	string "DHCP pool start address"
	default "***********"
	help
	  Start IP address for DHCP pool.

config CELLULAR_WIFI_ROUTER_DHCP_POOL_SIZE
	int "DHCP pool size"
	default 10
	range 1 50
	help
	  Number of IP addresses in DHCP pool.

config CELLULAR_WIFI_ROUTER_MAX_CLIENTS
	int "Maximum WiFi clients"
	default 10
	range 1 20
	help
	  Maximum number of concurrent WiFi clients.

config CELLULAR_WIFI_ROUTER_POWER_SAVE_TIMEOUT
	int "Power save timeout (seconds)"
	default 300
	help
	  Timeout in seconds before entering power save mode.
	  Set to 0 to disable power save.

endmenu
