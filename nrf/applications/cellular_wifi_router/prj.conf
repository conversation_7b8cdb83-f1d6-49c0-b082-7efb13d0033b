# Cellular WiFi Router Configuration
# Copyright (c) 2024 Nordic Semiconductor ASA
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause

# Disable bootloaders to save memory
CONFIG_BOOTLOADER_MCUBOOT=n
CONFIG_SECURE_BOOT=n
CONFIG_BUILD_WITH_TFM=n

# System configuration
CONFIG_MAIN_STACK_SIZE=4096
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048
CONFIG_HEAP_MEM_POOL_SIZE=16384
CONFIG_NEWLIB_LIBC=y
CONFIG_PICOLIBC=y

# Logging
CONFIG_LOG=y
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_BACKEND_UART=y
CONFIG_LOG_MODE_DEFERRED=y

# ZBUS for inter-module communication
CONFIG_ZBUS=y
CONFIG_ZBUS_CHANNEL_NAME=y
CONFIG_ZBUS_OBSERVER_NAME=y
CONFIG_ZBUS_MSG_SUBSCRIBER_NET_BUF_POOL_SIZE=16

# State Machine Framework
CONFIG_SMF=y

# Networking stack
CONFIG_NETWORKING=y
CONFIG_NET_NATIVE=y
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_UDP=y
CONFIG_NET_TCP=y
CONFIG_NET_DHCPV4=y
CONFIG_NET_L2_ETHERNET=y

# DHCP Server
CONFIG_NET_DHCPV4_SERVER=y
CONFIG_NET_DHCPV4_SERVER_INSTANCES=1

# WiFi configuration
CONFIG_WIFI=y
CONFIG_WIFI_NRF70=y
CONFIG_WIFI_NRF70_SKIP_LOCAL_ADMIN_MAC=y
CONFIG_NRF_WIFI_SCAN_MAX_BSS_CNT=10

# WiFi SoftAP mode
CONFIG_NET_L2_WIFI_MGMT=y
CONFIG_WIFI_NM_WPA_SUPPLICANT=n

# Cellular/LTE configuration
CONFIG_LTE_LINK_CONTROL=y
CONFIG_PDN=y
CONFIG_MODEM_INFO=y
CONFIG_AT_HOST_LIBRARY=y

# nRF Modem library
CONFIG_NRF_MODEM_LIB=y
CONFIG_NRF_MODEM_LIB_SYS_INIT=y

# Power management
CONFIG_PM=y
CONFIG_PM_DEVICE=y
CONFIG_PM_DEVICE_RUNTIME=y

# GPIO and UI
CONFIG_GPIO=y
CONFIG_PWM=y
CONFIG_LED=y
CONFIG_LED_PWM=y

# Flash and storage
CONFIG_FLASH=y
CONFIG_FLASH_PAGE_LAYOUT=y
CONFIG_NVS=y
CONFIG_SETTINGS=y
CONFIG_SETTINGS_NVS=y

# Security
CONFIG_MBEDTLS=y
CONFIG_NORDIC_SECURITY_BACKEND=y

# Debug and development
CONFIG_ASSERT=y
CONFIG_DEBUG_INFO=y
CONFIG_THREAD_NAME=y
CONFIG_THREAD_STACK_INFO=y

# Memory optimization
CONFIG_NET_BUF_RX_COUNT=16
CONFIG_NET_BUF_TX_COUNT=16
CONFIG_NET_PKT_RX_COUNT=8
CONFIG_NET_PKT_TX_COUNT=8
