#
# Copyright (c) 2024 Nordic Semiconductor ASA
#
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause
#

# General
CONFIG_NEWLIB_LIBC=y
CONFIG_PICOLIBC=y
CONFIG_HW_STACK_PROTECTION=y
CONFIG_HW_ID_LIBRARY_SOURCE_IMEI=y

# Logging
CONFIG_LOG=y
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_BACKEND_UART=y

# ZBUS for inter-module communication
CONFIG_ZBUS=y
CONFIG_ZBUS_CHANNEL_NAME=y
CONFIG_ZBUS_RUNTIME_OBSERVERS=y
CONFIG_ZBUS_MSG_SUBSCRIBER=y

# SMF for state machines
CONFIG_SMF=y
CONFIG_SMF_INITIAL_EVENT=y

# Networking
CONFIG_NETWORKING=y
CONFIG_NET_NATIVE=y
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_DHCPV4=y
CONFIG_NET_ROUTE=y
CONFIG_NET_L2_ETHERNET=y
CONFIG_NET_ETHERNET_BRIDGE=y
CONFIG_NET_PROMISCUOUS_MODE=y

# DHCP Server
CONFIG_NET_DHCPV4_SERVER=y
CONFIG_NET_DHCPV4_SERVER_INSTANCES=1

# WiFi Support
CONFIG_WIFI=y
CONFIG_WIFI_NRF70=y
CONFIG_WIFI_NRF70_SKIP_LOCAL_ADMIN_MAC=y
CONFIG_NRF_WIFI_SCAN_MAX_BSS_CNT=10

# WiFi SoftAP
CONFIG_NET_L2_ETHERNET=y
CONFIG_NET_STATISTICS=y
CONFIG_NET_STATISTICS_WIFI=y
CONFIG_NET_STATISTICS_USER_API=y

# Cellular/LTE
CONFIG_LTE_LINK_CONTROL=y
CONFIG_PDN=y
CONFIG_MODEM_INFO=y
CONFIG_AT_HOST_LIBRARY=y

# Modem library
CONFIG_NRF_MODEM_LIB=y

# Power Management
CONFIG_PM=y
CONFIG_PM_DEVICE=y
CONFIG_PM_DEVICE_RUNTIME=y

# UI Support (can be disabled for power saving)
CONFIG_CELLULAR_WIFI_ROUTER_UI_ENABLED=y
CONFIG_DK_LIBRARY=y
CONFIG_PWM=y

# Memory optimization
CONFIG_MAIN_STACK_SIZE=4096
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048
CONFIG_NET_PKT_RX_COUNT=16
CONFIG_NET_PKT_TX_COUNT=16
CONFIG_NET_BUF_RX_COUNT=32
CONFIG_NET_BUF_TX_COUNT=32

# Flash and storage
CONFIG_FLASH=y
CONFIG_FLASH_PAGE_LAYOUT=y
CONFIG_MPU_ALLOW_FLASH_WRITE=y
CONFIG_SETTINGS=y
CONFIG_SETTINGS_NVS=y
CONFIG_NVS=y

# Heap memory pool
CONFIG_HEAP_MEM_POOL_SIZE=16384
