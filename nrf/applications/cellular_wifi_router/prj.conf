# Cellular WiFi Router Configuration
# Copyright (c) 2024 Nordic Semiconductor ASA
# SPDX-License-Identifier: LicenseRef-Nordic-5-Clause

# Disable bootloaders to save memory
CONFIG_BOOTLOADER_MCUBOOT=n
CONFIG_SECURE_BOOT=n
CONFIG_BUILD_WITH_TFM=n

# Disable partition manager for bootloader-free build
CONFIG_PARTITION_MANAGER_ENABLED=n

# Disable secure boot features
CONFIG_FW_INFO=n

# System configuration
CONFIG_MAIN_STACK_SIZE=4096
CONFIG_SYSTEM_WORKQUEUE_STACK_SIZE=2048
CONFIG_HEAP_MEM_POOL_SIZE=16384
CONFIG_NEWLIB_LIBC=y
CONFIG_PICOLIBC=y
CONFIG_REBOOT=y

# Logging
CONFIG_LOG=y
CONFIG_LOG_DEFAULT_LEVEL=3
CONFIG_LOG_BACKEND_UART=y
CONFIG_LOG_MODE_DEFERRED=y

# ZBUS for inter-module communication
CONFIG_ZBUS=y
CONFIG_ZBUS_CHANNEL_NAME=y
CONFIG_ZBUS_OBSERVER_NAME=y
CONFIG_ZBUS_MSG_SUBSCRIBER=y

# State Machine Framework
CONFIG_SMF=y

# Networking stack
CONFIG_NETWORKING=y
CONFIG_NET_NATIVE=y
CONFIG_NET_IPV4=y
CONFIG_NET_IPV6=n
CONFIG_NET_UDP=y
CONFIG_NET_TCP=y
CONFIG_NET_DHCPV4=y
CONFIG_NET_L2_ETHERNET=y

# PPP for cellular - disabled for now due to dependency issues
# CONFIG_NET_PPP=y
# CONFIG_NET_L2_PPP=y

# Network management
CONFIG_NET_MGMT=y
CONFIG_NET_MGMT_EVENT=y
CONFIG_NET_MGMT_EVENT_INFO=y

# DHCP Server
CONFIG_NET_DHCPV4_SERVER=y
CONFIG_NET_DHCPV4_SERVER_INSTANCES=1

# WiFi configuration
CONFIG_WIFI=y
CONFIG_WIFI_NRF70=y

# WiFi SoftAP mode
CONFIG_NET_L2_WIFI_MGMT=y

# Cellular/LTE configuration
CONFIG_LTE_LINK_CONTROL=y
CONFIG_PDN=y
CONFIG_MODEM_INFO=y
CONFIG_AT_HOST_LIBRARY=y

# Enable network drivers for PPP support
CONFIG_NET_DRIVERS=y

# AT command support
CONFIG_AT_PARSER=y
CONFIG_AT_MONITOR=y
CONFIG_AT_CMD_CUSTOM=y

# nRF Modem library
CONFIG_NRF_MODEM_LIB=y

# PSM (Power Saving Mode) - enable module but not request
CONFIG_LTE_LC_PSM_MODULE=y
# CONFIG_LTE_PSM_REQ=y

# Power management
CONFIG_PM=y
CONFIG_PM_DEVICE=y
CONFIG_PM_DEVICE_RUNTIME=y

# GPIO and UI
CONFIG_GPIO=y
CONFIG_PWM=y
CONFIG_LED=y
CONFIG_LED_PWM=y

# UART for AT commands
CONFIG_UART_INTERRUPT_DRIVEN=y
CONFIG_UART_ASYNC_API=y

# Flash and storage - simplified configuration
CONFIG_FLASH=y
CONFIG_NVS=y
CONFIG_SETTINGS=y

# Disable problematic SOC flash driver
CONFIG_SOC_FLASH_NRF=n

# Use alternative flash configuration
CONFIG_FLASH_SIMULATOR=n

# Security
CONFIG_MBEDTLS=y
CONFIG_NORDIC_SECURITY_BACKEND=y

# Disable problematic entropy driver
CONFIG_ENTROPY_CC3XX=n

# Debug and development
CONFIG_ASSERT=y
CONFIG_DEBUG_INFO=y
CONFIG_THREAD_NAME=y
CONFIG_THREAD_STACK_INFO=y

# Memory optimization
CONFIG_NET_BUF_RX_COUNT=16
CONFIG_NET_BUF_TX_COUNT=16
CONFIG_NET_PKT_RX_COUNT=8
CONFIG_NET_PKT_TX_COUNT=8
